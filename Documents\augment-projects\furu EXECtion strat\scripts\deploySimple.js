async function main() {
    console.log("🚀 SIMPLE COLLATERAL FARMING DEPLOYMENT");
    console.log("═".repeat(50));

    const hre = require("hardhat");
    const [deployer] = await hre.ethers.getSigners();
    
    console.log("Deployer:", deployer.address);
    
    const balance = await hre.ethers.provider.getBalance(deployer.address);
    console.log("Balance:", hre.ethers.formatEther(balance), "ETH");
    
    // Get current gas price
    const feeData = await hre.ethers.provider.getFeeData();
    console.log("Gas Price:", hre.ethers.formatUnits(feeData.gasPrice || 0n, 'gwei'), "gwei");
    
    console.log("\n🔧 Deploying CollateralLoopFarming...");
    
    try {
        const CollateralLoopFarming = await hre.ethers.getContractFactory("CollateralLoopFarming");
        
        // Deploy with fixed gas settings to avoid estimation issues
        const contract = await CollateralLoopFarming.deploy({
            gasLimit: 3000000, // Fixed 3M gas
            gasPrice: feeData.gasPrice || hre.ethers.parseUnits('10', 'gwei') // Use current or 10 gwei
        });
        
        console.log("⏳ Waiting for deployment...");
        await contract.waitForDeployment();
        
        const address = await contract.getAddress();
        console.log("✅ Deployed to:", address);
        
        // Verify contract has code
        const code = await hre.ethers.provider.getCode(address);
        if (code === "0x") {
            throw new Error("No code at address");
        }
        console.log("✅ Contract verified");
        
        // Test basic function
        try {
            const stats = await contract.getStats();
            console.log("✅ Contract functional");
            console.log("   Profit:", hre.ethers.formatUnits(stats.totalProfit, 6), "USDC");
            console.log("   Executions:", stats.totalExecutions.toString());
        } catch (error) {
            console.log("⚠️  Function test failed:", error.message);
        }
        
        // Save deployment info
        const deploymentInfo = {
            address: address,
            deployer: deployer.address,
            timestamp: new Date().toISOString(),
            network: "mainnet"
        };
        
        const fs = require('fs');
        fs.writeFileSync('deployed-simple.json', JSON.stringify(deploymentInfo, null, 2));
        
        console.log("\n🎉 DEPLOYMENT SUCCESSFUL!");
        console.log("Address:", address);
        console.log("Saved to: deployed-simple.json");
        
        return address;
        
    } catch (error) {
        console.error("❌ Deployment failed:", error.message);
        throw error;
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
