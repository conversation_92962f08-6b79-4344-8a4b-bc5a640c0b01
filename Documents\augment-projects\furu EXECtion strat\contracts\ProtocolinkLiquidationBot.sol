// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IAaveV3Pool {
    function liquidationCall(
        address collateralAsset,
        address debtAsset,
        address user,
        uint256 debtToCover,
        bool receiveAToken
    ) external;
    
    function getUserAccountData(address user)
        external
        view
        returns (
            uint256 totalCollateralBase,
            uint256 totalDebtBase,
            uint256 availableBorrowsBase,
            uint256 currentLiquidationThreshold,
            uint256 ltv,
            uint256 healthFactor
        );
}

interface ICompoundV3 {
    function absorb(address absorber, address[] calldata accounts) external;
    function isLiquidatable(address account) external view returns (bool);
    function borrowBalanceOf(address account) external view returns (uint256);
}

interface IProtocolinkRouter {
    function execute(bytes calldata data) external payable;
}

/**
 * @title ProtocolinkLiquidationBot
 * @dev Automated liquidation bot using Protocolink for optimal routing
 * @notice Executes profitable liquidations across Aave V3, Compound V3, and other protocols
 */
contract ProtocolinkLiquidationBot is ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    // Core protocol addresses
    IBalancerVault public constant BALANCER_VAULT = IBalancerVault(******************************************);
    IAaveV3Pool public constant AAVE_V3_POOL = IAaveV3Pool(******************************************);
    ICompoundV3 public constant COMPOUND_V3_USDC = ICompoundV3(******************************************);
    ICompoundV3 public constant COMPOUND_V3_ETH = ICompoundV3(******************************************);
    IProtocolinkRouter public constant PROTOCOLINK_ROUTER = IProtocolinkRouter(******************************************);

    // Profit wallet for all earnings
    address public constant PROFIT_WALLET = ******************************************;

    // Minimum profit threshold (in USD, 18 decimals)
    uint256 public constant MIN_PROFIT_USD = 100 * 1e18; // $100 minimum

    // Events
    event LiquidationExecuted(
        string indexed protocol,
        address indexed user,
        address indexed collateralAsset,
        address debtAsset,
        uint256 debtAmount,
        uint256 profit
    );

    event CompoundAbsorption(
        address indexed market,
        address indexed user,
        uint256 profit
    );

    struct AaveLiquidationParams {
        address user;
        address collateralAsset;
        address debtAsset;
        uint256 debtToCover;
        bytes protocolinkSwapData;
        uint256 minProfitUSD;
    }

    struct CompoundAbsorptionParams {
        address market;
        address[] users;
        bytes protocolinkSwapData;
        uint256 minProfitUSD;
    }

    /**
     * @dev Execute Aave V3 liquidation with Protocolink routing
     * @param params Liquidation parameters including user, assets, and routing data
     */
    function executeAaveLiquidation(AaveLiquidationParams calldata params) external onlyOwner nonReentrant {
        require(params.minProfitUSD >= MIN_PROFIT_USD, "Profit below minimum threshold");
        
        // Verify position is liquidatable
        (, , , , , uint256 healthFactor) = AAVE_V3_POOL.getUserAccountData(params.user);
        require(healthFactor < 1e18, "Position not liquidatable");

        // Prepare flash loan
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        tokens[0] = params.debtAsset;
        amounts[0] = params.debtToCover;

        bytes memory userData = abi.encode(
            "AAVE_LIQUIDATION",
            params.user,
            params.collateralAsset,
            params.debtAsset,
            params.debtToCover,
            params.protocolinkSwapData,
            params.minProfitUSD
        );

        BALANCER_VAULT.flashLoan(address(this), tokens, amounts, userData);
    }

    /**
     * @dev Execute Compound V3 absorption with Protocolink routing
     * @param params Absorption parameters including market, users, and routing data
     */
    function executeCompoundAbsorption(CompoundAbsorptionParams calldata params) external onlyOwner nonReentrant {
        require(params.minProfitUSD >= MIN_PROFIT_USD, "Profit below minimum threshold");
        
        // Verify at least one user is liquidatable
        bool hasLiquidatable = false;
        for (uint i = 0; i < params.users.length; i++) {
            if (ICompoundV3(params.market).isLiquidatable(params.users[i])) {
                hasLiquidatable = true;
                break;
            }
        }
        require(hasLiquidatable, "No liquidatable positions");

        // Calculate total debt to cover
        uint256 totalDebt = 0;
        for (uint i = 0; i < params.users.length; i++) {
            totalDebt += ICompoundV3(params.market).borrowBalanceOf(params.users[i]);
        }

        // Prepare flash loan for USDC (Compound V3 USDC market)
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        tokens[0] = 0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48; // USDC
        amounts[0] = totalDebt;

        bytes memory userData = abi.encode(
            "COMPOUND_ABSORPTION",
            params.market,
            params.users,
            params.protocolinkSwapData,
            params.minProfitUSD
        );

        BALANCER_VAULT.flashLoan(address(this), tokens, amounts, userData);
    }

    /**
     * @dev Balancer flash loan callback
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external {
        require(msg.sender == address(BALANCER_VAULT), "Unauthorized callback");

        string memory operation = abi.decode(userData, (string));

        if (keccak256(bytes(operation)) == keccak256(bytes("AAVE_LIQUIDATION"))) {
            _executeAaveLiquidationCallback(tokens[0], amounts[0], userData);
        } else if (keccak256(bytes(operation)) == keccak256(bytes("COMPOUND_ABSORPTION"))) {
            _executeCompoundAbsorptionCallback(tokens[0], amounts[0], userData);
        }

        // Repay flash loan (Balancer V2 has 0% fee)
        IERC20(tokens[0]).safeTransfer(address(BALANCER_VAULT), amounts[0]);
    }

    /**
     * @dev Internal Aave liquidation execution
     */
    function _executeAaveLiquidationCallback(
        address debtAsset,
        uint256 debtAmount,
        bytes memory userData
    ) internal {
        (
            ,
            address user,
            address collateralAsset,
            ,
            ,
            bytes memory protocolinkSwapData,
            uint256 minProfitUSD
        ) = abi.decode(userData, (string, address, address, address, uint256, bytes, uint256));

        uint256 initialBalance = IERC20(debtAsset).balanceOf(address(this));

        // Execute liquidation on Aave V3
        IERC20(debtAsset).safeApprove(address(AAVE_V3_POOL), debtAmount);
        AAVE_V3_POOL.liquidationCall(
            collateralAsset,
            debtAsset,
            user,
            debtAmount,
            false // Receive underlying asset, not aToken
        );

        // Get received collateral
        uint256 collateralReceived = IERC20(collateralAsset).balanceOf(address(this));
        require(collateralReceived > 0, "No collateral received");

        // Swap collateral back to debt asset via Protocolink for optimal routing
        IERC20(collateralAsset).safeApprove(address(PROTOCOLINK_ROUTER), collateralReceived);
        
        // Execute Protocolink swap
        PROTOCOLINK_ROUTER.execute(protocolinkSwapData);

        uint256 finalBalance = IERC20(debtAsset).balanceOf(address(this));
        uint256 profit = finalBalance - initialBalance;

        require(profit > 0, "No profit generated");

        // Send profit to profit wallet
        IERC20(debtAsset).safeTransfer(PROFIT_WALLET, profit);

        emit LiquidationExecuted("Aave V3", user, collateralAsset, debtAsset, debtAmount, profit);
    }

    /**
     * @dev Internal Compound absorption execution
     */
    function _executeCompoundAbsorptionCallback(
        address asset,
        uint256 amount,
        bytes memory userData
    ) internal {
        (
            ,
            address market,
            address[] memory users,
            bytes memory protocolinkSwapData,
            uint256 minProfitUSD
        ) = abi.decode(userData, (string, address, address[], bytes, uint256));

        uint256 initialBalance = IERC20(asset).balanceOf(address(this));

        // Execute absorption on Compound V3
        IERC20(asset).safeApprove(market, amount);
        ICompoundV3(market).absorb(address(this), users);

        // Get received collateral (various assets)
        // Note: Compound V3 absorption gives us the collateral assets directly
        // We would need to identify which assets we received and swap them

        // For simplicity, assuming we received the same asset back with profit
        uint256 finalBalance = IERC20(asset).balanceOf(address(this));
        uint256 profit = finalBalance - initialBalance;

        require(profit > 0, "No absorption profit");

        // Send profit to profit wallet
        IERC20(asset).safeTransfer(PROFIT_WALLET, profit);

        emit CompoundAbsorption(market, users[0], profit);
    }

    /**
     * @dev Emergency function to recover stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(PROFIT_WALLET, amount);
    }

    /**
     * @dev Check if Aave position is liquidatable
     */
    function isAaveLiquidatable(address user) external view returns (bool, uint256) {
        (, , , , , uint256 healthFactor) = AAVE_V3_POOL.getUserAccountData(user);
        return (healthFactor < 1e18, healthFactor);
    }

    /**
     * @dev Check if Compound position is liquidatable
     */
    function isCompoundLiquidatable(address market, address user) external view returns (bool) {
        return ICompoundV3(market).isLiquidatable(user);
    }

    /**
     * @dev Get Aave user position data
     */
    function getAavePositionData(address user) external view returns (
        uint256 totalCollateralBase,
        uint256 totalDebtBase,
        uint256 healthFactor
    ) {
        (totalCollateralBase, totalDebtBase, , , , healthFactor) = AAVE_V3_POOL.getUserAccountData(user);
    }

    /**
     * @dev Get Compound user debt
     */
    function getCompoundDebt(address market, address user) external view returns (uint256) {
        return ICompoundV3(market).borrowBalanceOf(user);
    }
}
