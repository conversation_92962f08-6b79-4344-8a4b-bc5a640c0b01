import { ethers } from "ethers";
import * as dotenv from "dotenv";
import * as fs from "fs";
import * as path from "path";

dotenv.config();

// Get environment variables
const PRIVATE_KEY = process.env.PRIVATE_KEY;
const MAINNET_RPC_URL = process.env.MAINNET_RPC_URL;

if (!PRIVATE_KEY || !MAINNET_RPC_URL) {
  throw new Error("Missing required environment variables: PRIVATE_KEY or MAINNET_RPC_URL");
}

/**
 * Deploy Zero Capital Yield Farmer Contract
 * 
 * This script deploys the advanced flash loan-powered yield farming contract
 * that requires ZERO initial capital investment for yield farming operations.
 */

async function main() {
  console.log("🚀 DEPLOYING ZERO CAPITAL YIELD FARMER CONTRACT");
  console.log("═".repeat(60));

  try {
    // Initialize provider and wallet
    const provider = new ethers.JsonRpcProvider(MAINNET_RPC_URL);
    const wallet = new ethers.Wallet(PRIVATE_KEY, provider);

    const deployerAddress = wallet.address;
    const balance = await provider.getBalance(deployerAddress);
    const balanceETH = parseFloat(ethers.formatEther(balance));

    console.log("💰 DEPLOYMENT WALLET INFO:");
    console.log(`   Deployer: ${deployerAddress}`);
    console.log(`   Balance: ${balanceETH.toFixed(4)} ETH`);
    console.log(`   Network: ${(await provider.getNetwork()).name}`);

    // Check if we have enough ETH for deployment
    if (balanceETH < 0.005) {
      throw new Error("Insufficient ETH for deployment. Need at least 0.005 ETH.");
    }

    // Get current gas price
    const feeData = await provider.getFeeData();
    const gasPrice = feeData.gasPrice || BigInt(0);
    const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, "gwei"));

    console.log("\n⛽ GAS CONDITIONS:");
    console.log(`   Gas Price: ${gasPriceGwei.toFixed(1)} gwei`);
    console.log(`   Estimated Deployment Cost: ~0.003 ETH`);

    // Load contract bytecode and ABI
    const contractPath = path.join(__dirname, '..', 'artifacts', 'contracts', 'ZeroCapitalYieldFarmer.sol', 'ZeroCapitalYieldFarmer.json');

    if (!fs.existsSync(contractPath)) {
      throw new Error(`Contract artifact not found at ${contractPath}. Run 'npm run compile' first.`);
    }

    const contractArtifact = JSON.parse(fs.readFileSync(contractPath, 'utf8'));
    const contractFactory = new ethers.ContractFactory(contractArtifact.abi, contractArtifact.bytecode, wallet);

    console.log("\n🔨 DEPLOYING CONTRACT...");

    const deploymentTx = await contractFactory.deploy({
      gasLimit: 3000000, // 3M gas limit
      maxFeePerGas: ethers.parseUnits("50", "gwei"), // Max 50 gwei
      maxPriorityFeePerGas: ethers.parseUnits("2", "gwei") // 2 gwei priority
    });

    console.log(`   📝 Deployment transaction: ${deploymentTx.deploymentTransaction()?.hash}`);
    console.log("   ⏳ Waiting for confirmation...");

    // Wait for deployment
    await deploymentTx.waitForDeployment();
    const contractAddress = await deploymentTx.getAddress();

    console.log("\n✅ CONTRACT DEPLOYED SUCCESSFULLY!");
    console.log(`   📍 Contract Address: ${contractAddress}`);
    console.log(`   🔗 Transaction Hash: ${deploymentTx.deploymentTransaction()?.hash}`);

    // Verify deployment
    const deployedContract = new ethers.Contract(contractAddress, contractArtifact.abi, wallet);
    
    // Check contract state
    console.log("\n🔍 VERIFYING CONTRACT STATE...");
    
    try {
      const profitWallet = "******************************************";
      console.log(`   💰 Profit Wallet: ${profitWallet}`);
      
      // Check if strategies are enabled
      for (let i = 1; i <= 8; i++) {
        const isEnabled = await deployedContract.isStrategyEnabled(i);
        console.log(`   📊 Strategy ${i}: ${isEnabled ? "✅ Enabled" : "❌ Disabled"}`);
      }

      // Check if deployer is authorized
      const isAuthorized = await deployedContract.isAuthorizedExecutor(deployerAddress);
      console.log(`   🔑 Deployer Authorized: ${isAuthorized ? "✅ Yes" : "❌ No"}`);

    } catch (error) {
      console.log("   ⚠️ Could not verify all contract state (this is normal for new deployments)");
    }

    console.log("\n📋 CONTRACT FEATURES:");
    console.log("   🎯 8 Zero-Capital Yield Farming Strategies");
    console.log("   💳 Flash Loan Integration (Balancer V2 + Aave V3)");
    console.log("   🔄 Self-Sustaining Gas Fee Management");
    console.log("   📈 Automatic Profit Reinvestment (10%)");
    console.log("   ⛽ Gas Reserve Allocation (5%)");
    console.log("   💰 Profit Wallet Integration");
    console.log("   🛡️ Circuit Breaker Protection");
    console.log("   🔧 Admin Controls & Emergency Functions");

    console.log("\n🚀 ZERO-CAPITAL STRATEGIES AVAILABLE:");
    console.log("   1. 🥩 Stake-Reward Farming");
    console.log("   2. 🌊 Liquidity Mining");
    console.log("   3. 🔄 Yield Token Arbitrage");
    console.log("   4. 🗳️ Governance Token Farming");
    console.log("   5. 🌉 Cross-Protocol Yield Arbitrage");
    console.log("   6. 📈 Leveraged Yield Farming");
    console.log("   7. 🎯 Reward Token Sniping");
    console.log("   8. 🔧 Compound Yield Optimization");

    console.log("\n💡 NEXT STEPS:");
    console.log("   1. 🔧 Configure strategy parameters if needed");
    console.log("   2. 🎯 Run yield farming opportunity scanner");
    console.log("   3. ⚡ Execute zero-capital yield farming strategies");
    console.log("   4. 🔄 Start unstoppable automated system");

    console.log("\n📝 DEPLOYMENT SUMMARY:");
    console.log(`   Contract: ZeroCapitalYieldFarmer`);
    console.log(`   Address: ${contractAddress}`);
    console.log(`   Network: Ethereum Mainnet`);
    console.log(`   Gas Used: ~3M gas`);
    console.log(`   Status: ✅ Ready for zero-capital yield farming`);

    // Save deployment info
    const deploymentInfo = {
      contractName: "ZeroCapitalYieldFarmer",
      contractAddress: contractAddress,
      deployerAddress: deployerAddress,
      transactionHash: deploymentTx.deploymentTransaction()?.hash,
      blockNumber: deploymentTx.deploymentTransaction()?.blockNumber,
      gasPrice: gasPrice.toString(),
      timestamp: new Date().toISOString(),
      network: (await provider.getNetwork()).name,
      features: [
        "Zero Capital Yield Farming",
        "Flash Loan Integration",
        "Self-Sustaining Gas Management",
        "Automatic Profit Distribution",
        "8 Yield Farming Strategies"
      ]
    };

    // Write deployment info to file
    const deploymentPath = path.join(__dirname, '..', 'deployed-zero-capital-contract.json');
    fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
    
    console.log(`\n💾 Deployment info saved to: ${deploymentPath}`);

    console.log("\n🎉 ZERO CAPITAL YIELD FARMING CONTRACT READY!");
    console.log("═".repeat(60));
    console.log("🚀 You can now execute yield farming strategies with ZERO capital!");
    console.log("💰 All profits will be sent to: ******************************************");
    console.log("⚡ Flash loans provide ALL the capital needed for farming");

  } catch (error) {
    console.error("\n❌ DEPLOYMENT FAILED:", error);
    
    if (error instanceof Error) {
      if (error.message.includes("insufficient funds")) {
        console.log("\n💡 SOLUTION: Add more ETH to your wallet for gas fees");
      } else if (error.message.includes("gas")) {
        console.log("\n💡 SOLUTION: Try deploying when gas prices are lower");
      } else if (error.message.includes("nonce")) {
        console.log("\n💡 SOLUTION: Wait a moment and try again (nonce issue)");
      }
    }
    
    console.log("\n🔧 TROUBLESHOOTING:");
    console.log("   1. Check wallet balance (need ~0.005 ETH)");
    console.log("   2. Verify network connection");
    console.log("   3. Check gas prices (may be too high)");
    console.log("   4. Ensure contract compiles successfully");
    
    process.exit(1);
  }
}

// Execute deployment
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
