const { ethers } = require("ethers");
const fs = require("fs");
require("dotenv").config();

// Contract bytecode and ABI (will be generated after compilation)
const contractArtifact = require("../artifacts/contracts/UltimateArbitrageBot.sol/UltimateArbitrageBot.json");

async function main() {
    console.log("🚀 DIRECT DEPLOYMENT - ULTIMATE ARBITRAGE BOT");
    console.log("═══════════════════════════════════════════════════════════");
    
    // Setup provider and wallet
    const provider = new ethers.JsonRpcProvider(`https://opt-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw`);
    
    // You need to set your private key in .env file
    if (!process.env.PRIVATE_KEY) {
        throw new Error("❌ PRIVATE_KEY not set in .env file");
    }
    
    const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
    console.log("Deploying with wallet:", wallet.address);
    
    // Check balance
    const balance = await provider.getBalance(wallet.address);
    console.log("Wallet balance:", ethers.formatEther(balance), "ETH");
    
    if (balance < ethers.parseEther("0.2")) {
        throw new Error("❌ Insufficient ETH balance. Need at least 0.2 ETH for deployment and funding");
    }
    
    // Check network
    const network = await provider.getNetwork();
    console.log("Network:", network.name, "Chain ID:", network.chainId);
    
    if (network.chainId !== 10n) {
        throw new Error("❌ Must deploy on Optimism mainnet (Chain ID: 10)");
    }
    
    console.log("\n📋 CONTRACT SPECIFICATIONS:");
    console.log("✅ 4 Arbitrage Strategies: Cross-DEX, Liquidation, Yield, Refinancing");
    console.log("✅ Profit Wallet: ******************************************");
    console.log("✅ Min Profit: $500 per transaction");
    console.log("✅ Gas Limit: 1M gas maximum");
    console.log("✅ MEV Protection: Flashbots integration");
    
    console.log("\n🔧 DEPLOYING CONTRACT...");
    
    // Create contract factory
    const contractFactory = new ethers.ContractFactory(
        contractArtifact.abi,
        contractArtifact.bytecode,
        wallet
    );
    
    // Deploy contract
    console.log("⏳ Deploying contract...");
    const contract = await contractFactory.deploy({
        gasLimit: 5000000, // 5M gas limit
        gasPrice: ethers.parseUnits("0.001", "gwei") // 0.001 gwei for Optimism
    });
    
    console.log("⏳ Waiting for deployment confirmation...");
    await contract.waitForDeployment();
    
    const contractAddress = await contract.getAddress();
    console.log("✅ Contract deployed to:", contractAddress);
    
    // Verify deployment
    console.log("\n🔍 VERIFYING DEPLOYMENT...");
    
    const code = await provider.getCode(contractAddress);
    if (code === "0x") {
        throw new Error("❌ Contract deployment failed");
    }
    console.log("✅ Contract code verified");
    
    // Check constants
    const profitWallet = await contract.PROFIT_WALLET();
    const minProfit = await contract.MIN_PROFIT_THRESHOLD();
    const maxGas = await contract.MAX_GAS_LIMIT();
    
    console.log("✅ Profit wallet:", profitWallet);
    console.log("✅ Min profit threshold:", ethers.formatUnits(minProfit, 6), "USDC");
    console.log("✅ Max gas limit:", maxGas.toString());
    
    if (profitWallet !== "******************************************") {
        throw new Error("❌ Profit wallet mismatch");
    }
    
    // Test contract functions
    console.log("\n🧪 TESTING CONTRACT FUNCTIONS...");
    
    const stats = await contract.getStats();
    console.log("✅ getStats() working - Total profit:", stats[0].toString());
    
    const isAuthorized = await contract.authorizedCallers(wallet.address);
    console.log("✅ Authorization check - Deployer authorized:", isAuthorized);
    
    // Fund contract
    console.log("\n💰 FUNDING CONTRACT...");
    
    const fundingAmount = ethers.parseEther("0.1");
    const fundingTx = await wallet.sendTransaction({
        to: contractAddress,
        value: fundingAmount,
        gasLimit: 21000
    });
    
    await fundingTx.wait();
    console.log("✅ Contract funded with 0.1 ETH");
    console.log("📝 Funding tx:", fundingTx.hash);
    
    const contractBalance = await provider.getBalance(contractAddress);
    console.log("✅ Contract balance:", ethers.formatEther(contractBalance), "ETH");
    
    console.log("\n🎯 DEPLOYMENT SUMMARY:");
    console.log("═══════════════════════════════════════════════════════════");
    console.log("📍 Contract Address:", contractAddress);
    console.log("🏦 Profit Wallet: ******************************************");
    console.log("⛽ Gas Funded: 0.1 ETH");
    console.log("🔧 Owner:", wallet.address);
    console.log("🌐 Network: Optimism Mainnet");
    console.log("🔗 Etherscan:", `https://optimistic.etherscan.io/address/${contractAddress}`);
    
    // Save deployment info
    const deploymentInfo = {
        contractAddress: contractAddress,
        deployer: wallet.address,
        network: "optimism",
        chainId: "10",
        profitWallet: "******************************************",
        deploymentTime: new Date().toISOString(),
        fundingTxHash: fundingTx.hash,
        etherscanUrl: `https://optimistic.etherscan.io/address/${contractAddress}`
    };
    
    fs.writeFileSync('deployment-info.json', JSON.stringify(deploymentInfo, null, 2));
    console.log("💾 Deployment info saved to deployment-info.json");
    
    console.log("\n🚀 CONTRACT IS LIVE AND READY FOR REAL ARBITRAGE!");
    
    return deploymentInfo;
}

if (require.main === module) {
    main()
        .then(() => {
            console.log("\n🎉 DEPLOYMENT SUCCESSFUL!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("\n💥 DEPLOYMENT FAILED:");
            console.error(error);
            process.exit(1);
        });
}

module.exports = main;
