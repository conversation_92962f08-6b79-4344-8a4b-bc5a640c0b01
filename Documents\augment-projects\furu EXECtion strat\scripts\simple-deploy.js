async function main() {
    console.log("🚀 DEPLOYING ULTIMATE ARBITRAGE BOT - FINAL PRODUCTION CONTRACT");
    console.log("═══════════════════════════════════════════════════════════════");

    // Get hardhat runtime environment
    const hre = require("hardhat");

    const [deployer] = await hre.ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    
    const balance = await hre.ethers.provider.getBalance(deployer.address);
    console.log("Account balance:", hre.ethers.formatEther(balance), "ETH");

    // Check network
    const network = await hre.ethers.provider.getNetwork();
    console.log("Network:", network.name, "Chain ID:", network.chainId);
    
    console.log("\n📋 CONTRACT SPECIFICATIONS:");
    console.log("✅ 4 Arbitrage Strategies: Cross-DEX, Liquidation, Yield, Refinancing");
    console.log("✅ Profit Wallet: ******************************************");
    console.log("✅ Min Profit: $500 per transaction");
    console.log("✅ Gas Limit: 1M gas maximum");
    console.log("✅ MEV Protection: Flashbots integration");
    console.log("✅ Circuit Breaker: 3 failed transactions");
    
    console.log("\n🔧 DEPLOYING CONTRACT...");
    
    // Deploy the contract
    const UltimateArbitrageBot = await hre.ethers.getContractFactory("UltimateArbitrageBot");

    console.log("⏳ Deploying contract...");
    const contract = await UltimateArbitrageBot.deploy();

    console.log("⏳ Waiting for deployment confirmation...");
    await contract.waitForDeployment();

    const contractAddress = await contract.getAddress();
    console.log("✅ Contract deployed to:", contractAddress);

    // Verify deployment
    console.log("\n🔍 VERIFYING DEPLOYMENT...");

    // Check contract code
    const code = await hre.ethers.provider.getCode(contractAddress);
    if (code === "0x") {
        throw new Error("❌ Contract deployment failed - no code at address");
    }
    console.log("✅ Contract code verified");
    
    // Check constants
    try {
        const profitWallet = await contract.PROFIT_WALLET();
        const minProfit = await contract.MIN_PROFIT_THRESHOLD();
        const maxGas = await contract.MAX_GAS_LIMIT();
        
        console.log("✅ Profit wallet:", profitWallet);
        console.log("✅ Min profit threshold:", hre.ethers.formatUnits(minProfit, 6), "USDC");
        console.log("✅ Max gas limit:", maxGas.toString());
        
        if (profitWallet !== "******************************************") {
            throw new Error("❌ Profit wallet mismatch");
        }
        
    } catch (error) {
        console.error("❌ Contract verification failed:", error.message);
        throw error;
    }
    
    // Test contract functions
    console.log("\n🧪 TESTING CONTRACT FUNCTIONS...");
    
    try {
        const stats = await contract.getStats();
        console.log("✅ getStats() working - Total profit:", stats[0].toString());
        
        const isAuthorized = await contract.authorizedCallers(deployer.address);
        console.log("✅ Authorization check - Deployer authorized:", isAuthorized);
        
    } catch (error) {
        console.error("❌ Contract function test failed:", error.message);
        throw error;
    }
    
    console.log("\n💰 FUNDING CONTRACT FOR OPERATIONS...");
    
    // Send small amount of ETH for gas
    const fundingAmount = hre.ethers.parseEther("0.1"); // 0.1 ETH for gas
    const fundingTx = await deployer.sendTransaction({
        to: contractAddress,
        value: fundingAmount,
        gasLimit: 21000
    });

    await fundingTx.wait();
    console.log("✅ Contract funded with 0.1 ETH for gas");

    // Verify contract balance
    const contractBalance = await hre.ethers.provider.getBalance(contractAddress);
    console.log("✅ Contract ETH balance:", hre.ethers.formatEther(contractBalance), "ETH");
    
    console.log("\n🎯 DEPLOYMENT SUMMARY:");
    console.log("═══════════════════════════════════════════════════════════════");
    console.log("📍 Contract Address:", contractAddress);
    console.log("🏦 Profit Wallet: ******************************************");
    console.log("⛽ Gas Funded: 0.1 ETH");
    console.log("🔧 Owner:", deployer.address);
    console.log("🌐 Network:", network.name);
    
    console.log("\n📋 NEXT STEPS:");
    console.log("1. ✅ Contract deployed and verified");
    console.log("2. 🔄 Run opportunity detection to find arbitrage");
    console.log("3. 💰 Execute profitable strategies");
    console.log("4. 📊 Monitor profits in wallet");
    
    console.log("\n🚀 CONTRACT IS LIVE AND READY FOR PROFIT GENERATION!");
    
    // Save deployment info
    const deploymentInfo = {
        contractAddress: contractAddress,
        deployer: deployer.address,
        network: network.name,
        chainId: network.chainId.toString(),
        profitWallet: "******************************************",
        deploymentTime: new Date().toISOString(),
        fundingAmount: "0.1"
    };
    
    const fs = require('fs');
    fs.writeFileSync(
        'deployment-info.json', 
        JSON.stringify(deploymentInfo, null, 2)
    );
    
    console.log("💾 Deployment info saved to deployment-info.json");
    
    return {
        contract: contract,
        address: contractAddress,
        deployer: deployer.address
    };
}

// Execute deployment
main()
    .then(() => {
        console.log("\n🎉 ULTIMATE ARBITRAGE BOT DEPLOYMENT SUCCESSFUL!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n💥 DEPLOYMENT FAILED:");
        console.error(error);
        process.exit(1);
    });
