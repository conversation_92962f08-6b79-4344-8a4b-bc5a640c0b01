// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

/**
 * @title LPRewardHarvesting
 * @dev Flash-minted LP reward harvesting strategy
 * Targets misconfigured gauges on Convex, Balancer, Beethoven, Velodrome
 */

// ============ INTERFACES ============

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
    
    function joinPool(
        bytes32 poolId,
        address sender,
        address recipient,
        JoinPoolRequest memory request
    ) external payable;
    
    function exitPool(
        bytes32 poolId,
        address sender,
        address recipient,
        ExitPoolRequest memory request
    ) external;
    
    struct JoinPoolRequest {
        address[] assets;
        uint256[] maxAmountsIn;
        bytes userData;
        bool fromInternalBalance;
    }
    
    struct ExitPoolRequest {
        address[] assets;
        uint256[] minAmountsOut;
        bytes userData;
        bool toInternalBalance;
    }
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

// Convex Interfaces
interface IConvexBooster {
    function deposit(uint256 _pid, uint256 _amount, bool _stake) external returns (bool);
    function withdraw(uint256 _pid, uint256 _amount) external returns (bool);
    function poolInfo(uint256 _pid) external view returns (
        address lptoken,
        address token,
        address gauge,
        address crvRewards,
        address stash,
        bool shutdown
    );
}

interface IConvexRewards {
    function stake(uint256 amount) external returns (bool);
    function withdraw(uint256 amount, bool claim) external returns (bool);
    function getReward() external returns (bool);
    function earned(address account) external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
}

// Curve Gauge Interface
interface ICurveGauge {
    function deposit(uint256 _value) external;
    function withdraw(uint256 _value) external;
    function claim_rewards() external;
    function claimable_reward(address _addr, address _token) external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
}

// Balancer Gauge Interface
interface IBalancerGauge {
    function deposit(uint256 _value) external;
    function withdraw(uint256 _value) external;
    function claim_rewards() external;
    function claimable_reward(address _user, address _reward_token) external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
}

// Velodrome Interfaces
interface IVelodromeGauge {
    function deposit(uint256 amount, uint256 tokenId) external;
    function withdraw(uint256 amount) external;
    function getReward(address account, address[] memory tokens) external;
    function earned(address token, address account) external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
}

// Uniswap V3 for swaps
interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }
    
    function exactInputSingle(ExactInputSingleParams calldata params) external payable returns (uint256 amountOut);
}

contract LPRewardHarvesting is IFlashLoanRecipient, ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    // ============ CONSTANTS ============
    
    address public constant PROFIT_WALLET = ******************************************;
    address public constant BALANCER_VAULT = ******************************************;
    
    // Protocol addresses
    address public constant CONVEX_BOOSTER = ******************************************;
    address public constant CURVE_3POOL = ******************************************;
    address public constant BALANCER_GAUGE_CONTROLLER = ******************************************;
    address public constant VELODROME_ROUTER = ******************************************; // Optimism
    address public constant UNISWAP_V3_ROUTER = ******************************************;
    
    // Tokens
    address public constant WETH = ******************************************;
    address public constant USDC = ******************************************;
    address public constant CRV = ******************************************;
    address public constant CVX = ******************************************;
    address public constant BAL = ******************************************;
    
    // Strategy parameters
    uint256 public constant MIN_PROFIT_THRESHOLD = 250000e6; // $250K USDC minimum
    uint256 public constant MAX_LP_AMOUNT = 2000000e18; // 2M LP tokens max
    uint256 public constant GAS_LIMIT = 1000000; // 1M gas limit
    uint256 public constant SNAPSHOT_BLOCKS = 1; // Blocks to wait for snapshot
    
    // ============ STATE VARIABLES ============
    
    uint256 public totalProfitGenerated;
    uint256 public totalStrategiesExecuted;
    uint256 public failedTransactionCount;
    bool public emergencyStop;
    
    // Pool tracking
    mapping(address => bool) public whitelistedPools;
    mapping(address => uint256) public lastHarvestTime;
    mapping(address => uint256) public poolProfits;
    
    // ============ EVENTS ============
    
    event LPRewardHarvested(
        address indexed pool,
        address indexed gauge,
        uint256 lpAmount,
        uint256 rewardAmount,
        uint256 profit
    );
    
    event PoolWhitelisted(address indexed pool, bool status);
    event EmergencyStopActivated(string reason);
    event ProfitWithdrawn(address indexed token, uint256 amount);
    
    // ============ ERRORS ============
    
    error InsufficientProfit();
    error InvalidPool();
    error EmergencyStopActive();
    error ExcessiveGasUsage();
    error UnauthorizedCallback();
    error PoolNotWhitelisted();
    
    // ============ MODIFIERS ============
    
    modifier onlyBalancerVault() {
        if (msg.sender != BALANCER_VAULT) revert UnauthorizedCallback();
        _;
    }
    
    modifier notInEmergencyStop() {
        if (emergencyStop) revert EmergencyStopActive();
        _;
    }
    
    modifier gasOptimized() {
        uint256 gasStart = gasleft();
        _;
        uint256 gasUsed = gasStart - gasleft();
        if (gasUsed > GAS_LIMIT) revert ExcessiveGasUsage();
    }

    // ============ CONSTRUCTOR ============
    
    constructor() {
        // Approve tokens for protocols
        IERC20(WETH).approve(BALANCER_VAULT, type(uint256).max);
        IERC20(USDC).approve(BALANCER_VAULT, type(uint256).max);
        IERC20(WETH).approve(UNISWAP_V3_ROUTER, type(uint256).max);
        IERC20(USDC).approve(UNISWAP_V3_ROUTER, type(uint256).max);
        IERC20(CRV).approve(UNISWAP_V3_ROUTER, type(uint256).max);
        IERC20(CVX).approve(UNISWAP_V3_ROUTER, type(uint256).max);
        IERC20(BAL).approve(UNISWAP_V3_ROUTER, type(uint256).max);
    }

    // ============ MAIN EXECUTION FUNCTIONS ============
    
    /**
     * @dev Execute LP reward harvesting strategy
     * @param flashLoanAmount Amount of tokens to flash loan
     * @param targetPool LP pool to target
     * @param gaugeAddress Gauge contract address
     * @param protocolType 1=Convex, 2=Balancer, 3=Velodrome, 4=Curve
     */
    function executeLPRewardHarvesting(
        uint256 flashLoanAmount,
        address targetPool,
        address gaugeAddress,
        uint8 protocolType
    ) external onlyOwner notInEmergencyStop gasOptimized {
        require(whitelistedPools[targetPool], "Pool not whitelisted");
        require(protocolType >= 1 && protocolType <= 4, "Invalid protocol type");
        require(flashLoanAmount <= MAX_LP_AMOUNT, "Excessive LP amount");
        
        // Prepare flash loan
        address[] memory tokens = new address[](1);
        tokens[0] = WETH;
        
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = flashLoanAmount;
        
        // Encode strategy parameters
        bytes memory userData = abi.encode(
            protocolType,
            targetPool,
            gaugeAddress,
            flashLoanAmount,
            block.timestamp
        );
        
        // Execute flash loan
        IBalancerVault(BALANCER_VAULT).flashLoan(
            address(this),
            tokens,
            amounts,
            userData
        );
    }

    /**
     * @dev Balancer flash loan callback - executes LP reward harvesting
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external override onlyBalancerVault {
        require(tokens.length == 1 && tokens[0] == WETH, "Invalid flash loan token");

        // Decode strategy parameters
        (uint8 protocolType, address targetPool, address gaugeAddress, uint256 flashLoanAmount, uint256 timestamp) =
            abi.decode(userData, (uint8, address, address, uint256, uint256));

        uint256 initialBalance = IERC20(WETH).balanceOf(address(this));
        uint256 gasStart = gasleft();

        // Execute strategy based on protocol type
        uint256 profit = _executeHarvestingStrategy(protocolType, targetPool, gaugeAddress, flashLoanAmount);

        // Verify minimum profit threshold
        require(profit >= MIN_PROFIT_THRESHOLD, "Insufficient profit");

        // Repay flash loan (Balancer V2 has 0% fee)
        IERC20(WETH).safeTransfer(BALANCER_VAULT, flashLoanAmount);

        // Send profits to profit wallet
        uint256 finalBalance = IERC20(WETH).balanceOf(address(this));
        if (finalBalance > 0) {
            IERC20(WETH).safeTransfer(PROFIT_WALLET, finalBalance);
        }

        // Update metrics
        totalProfitGenerated += profit;
        totalStrategiesExecuted++;
        poolProfits[targetPool] += profit;
        lastHarvestTime[targetPool] = block.timestamp;

        uint256 gasUsed = gasStart - gasleft();
        emit LPRewardHarvested(targetPool, gaugeAddress, flashLoanAmount, profit, profit);
    }

    // ============ STRATEGY IMPLEMENTATION ============

    /**
     * @dev Execute harvesting strategy based on protocol type
     */
    function _executeHarvestingStrategy(
        uint8 protocolType,
        address targetPool,
        address gaugeAddress,
        uint256 flashLoanAmount
    ) internal returns (uint256 profit) {
        if (protocolType == 1) {
            return _executeConvexHarvesting(targetPool, gaugeAddress, flashLoanAmount);
        } else if (protocolType == 2) {
            return _executeBalancerHarvesting(targetPool, gaugeAddress, flashLoanAmount);
        } else if (protocolType == 3) {
            return _executeVelodromeHarvesting(targetPool, gaugeAddress, flashLoanAmount);
        } else if (protocolType == 4) {
            return _executeCurveHarvesting(targetPool, gaugeAddress, flashLoanAmount);
        }
        revert InvalidPool();
    }

    /**
     * @dev Execute Convex reward harvesting
     */
    function _executeConvexHarvesting(
        address targetPool,
        address gaugeAddress,
        uint256 flashLoanAmount
    ) internal returns (uint256 profit) {
        // Step 1: Create large LP position
        uint256 lpTokens = _createLPPosition(targetPool, flashLoanAmount);

        // Step 2: Deposit to Convex gauge
        IERC20(targetPool).approve(CONVEX_BOOSTER, lpTokens);

        // Find pool ID for Convex
        uint256 poolId = _findConvexPoolId(targetPool);
        IConvexBooster(CONVEX_BOOSTER).deposit(poolId, lpTokens, true);

        // Step 3: Wait for reward snapshot (simulate block passage)
        // In production, this would be timed with reward distribution

        // Step 4: Claim rewards
        IConvexRewards rewards = IConvexRewards(gaugeAddress);
        rewards.getReward();

        // Step 5: Convert rewards to WETH
        uint256 crvBalance = IERC20(CRV).balanceOf(address(this));
        uint256 cvxBalance = IERC20(CVX).balanceOf(address(this));

        if (crvBalance > 0) {
            profit += _swapToWETH(CRV, crvBalance);
        }
        if (cvxBalance > 0) {
            profit += _swapToWETH(CVX, cvxBalance);
        }

        // Step 6: Exit position
        rewards.withdraw(lpTokens, false);
        _exitLPPosition(targetPool, lpTokens, flashLoanAmount);

        return profit;
    }

    /**
     * @dev Execute Balancer gauge harvesting
     */
    function _executeBalancerHarvesting(
        address targetPool,
        address gaugeAddress,
        uint256 flashLoanAmount
    ) internal returns (uint256 profit) {
        // Step 1: Create large LP position
        uint256 lpTokens = _createLPPosition(targetPool, flashLoanAmount);

        // Step 2: Deposit to Balancer gauge
        IERC20(targetPool).approve(gaugeAddress, lpTokens);
        IBalancerGauge(gaugeAddress).deposit(lpTokens);

        // Step 3: Trigger reward snapshot
        // Wait for next block to trigger reward calculation

        // Step 4: Claim BAL rewards
        IBalancerGauge(gaugeAddress).claim_rewards();

        // Step 5: Convert rewards to WETH
        uint256 balBalance = IERC20(BAL).balanceOf(address(this));
        if (balBalance > 0) {
            profit = _swapToWETH(BAL, balBalance);
        }

        // Step 6: Exit position
        IBalancerGauge(gaugeAddress).withdraw(lpTokens);
        _exitLPPosition(targetPool, lpTokens, flashLoanAmount);

        return profit;
    }

    /**
     * @dev Execute Velodrome harvesting (Optimism)
     */
    function _executeVelodromeHarvesting(
        address targetPool,
        address gaugeAddress,
        uint256 flashLoanAmount
    ) internal returns (uint256 profit) {
        // Step 1: Create large LP position
        uint256 lpTokens = _createLPPosition(targetPool, flashLoanAmount);

        // Step 2: Deposit to Velodrome gauge
        IERC20(targetPool).approve(gaugeAddress, lpTokens);
        IVelodromeGauge(gaugeAddress).deposit(lpTokens, 0);

        // Step 3: Claim VELO rewards
        address[] memory rewardTokens = new address[](1);
        rewardTokens[0] = ******************************************; // VELO token
        IVelodromeGauge(gaugeAddress).getReward(address(this), rewardTokens);

        // Step 4: Convert rewards to WETH
        uint256 veloBalance = IERC20(rewardTokens[0]).balanceOf(address(this));
        if (veloBalance > 0) {
            profit = _swapToWETH(rewardTokens[0], veloBalance);
        }

        // Step 5: Exit position
        IVelodromeGauge(gaugeAddress).withdraw(lpTokens);
        _exitLPPosition(targetPool, lpTokens, flashLoanAmount);

        return profit;
    }

    /**
     * @dev Execute Curve gauge harvesting
     */
    function _executeCurveHarvesting(
        address targetPool,
        address gaugeAddress,
        uint256 flashLoanAmount
    ) internal returns (uint256 profit) {
        // Step 1: Create large LP position
        uint256 lpTokens = _createLPPosition(targetPool, flashLoanAmount);

        // Step 2: Deposit to Curve gauge
        IERC20(targetPool).approve(gaugeAddress, lpTokens);
        ICurveGauge(gaugeAddress).deposit(lpTokens);

        // Step 3: Claim CRV rewards
        ICurveGauge(gaugeAddress).claim_rewards();

        // Step 4: Convert rewards to WETH
        uint256 crvBalance = IERC20(CRV).balanceOf(address(this));
        if (crvBalance > 0) {
            profit = _swapToWETH(CRV, crvBalance);
        }

        // Step 5: Exit position
        ICurveGauge(gaugeAddress).withdraw(lpTokens);
        _exitLPPosition(targetPool, lpTokens, flashLoanAmount);

        return profit;
    }

    // ============ HELPER FUNCTIONS ============

    /**
     * @dev Create LP position using flash loaned tokens
     */
    function _createLPPosition(address pool, uint256 amount) internal returns (uint256 lpTokens) {
        // This is a simplified implementation
        // In production, this would use the specific pool's join function
        // For Balancer pools, use joinPool with proper parameters

        // Convert WETH to required tokens for the pool
        // This is pool-specific logic that would need to be implemented
        // based on the pool's token composition

        return amount; // Simplified return
    }

    /**
     * @dev Exit LP position and recover underlying tokens
     */
    function _exitLPPosition(address pool, uint256 lpTokens, uint256 targetAmount) internal {
        // This is a simplified implementation
        // In production, this would use the specific pool's exit function
        // For Balancer pools, use exitPool with proper parameters
    }

    /**
     * @dev Find Convex pool ID for a given LP token
     */
    function _findConvexPoolId(address lpToken) internal pure returns (uint256) {
        // This would query the Convex registry to find the pool ID
        // Simplified implementation returns a default ID
        return 0;
    }

    /**
     * @dev Swap any token to WETH via Uniswap V3
     */
    function _swapToWETH(address tokenIn, uint256 amountIn) internal returns (uint256 amountOut) {
        if (tokenIn == WETH || amountIn == 0) return amountIn;

        IERC20(tokenIn).approve(UNISWAP_V3_ROUTER, amountIn);

        IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
            tokenIn: tokenIn,
            tokenOut: WETH,
            fee: 3000, // 0.3% fee tier
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: amountIn,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });

        return IUniswapV3Router(UNISWAP_V3_ROUTER).exactInputSingle(params);
    }

    // ============ ADMIN FUNCTIONS ============

    /**
     * @dev Whitelist pools for harvesting
     */
    function setPoolWhitelist(address pool, bool status) external onlyOwner {
        whitelistedPools[pool] = status;
        emit PoolWhitelisted(pool, status);
    }

    /**
     * @dev Batch whitelist pools
     */
    function batchSetPoolWhitelist(address[] calldata pools, bool status) external onlyOwner {
        for (uint256 i = 0; i < pools.length; i++) {
            whitelistedPools[pools[i]] = status;
            emit PoolWhitelisted(pools[i], status);
        }
    }

    /**
     * @dev Emergency stop mechanism
     */
    function setEmergencyStop(bool _stop) external onlyOwner {
        emergencyStop = _stop;
        if (_stop) {
            emit EmergencyStopActivated("Manual activation by owner");
        }
    }

    /**
     * @dev Circuit breaker for failed transactions
     */
    function incrementFailedTransactions() external onlyOwner {
        failedTransactionCount++;
        if (failedTransactionCount >= 3) {
            emergencyStop = true;
            emit EmergencyStopActivated("Circuit breaker: 3 failed transactions");
        }
    }

    /**
     * @dev Reset failed transaction counter
     */
    function resetFailedTransactions() external onlyOwner {
        failedTransactionCount = 0;
    }

    /**
     * @dev Withdraw any stuck tokens (emergency only)
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        require(emergencyStop, "Emergency stop not active");
        IERC20(token).safeTransfer(PROFIT_WALLET, amount);
        emit ProfitWithdrawn(token, amount);
    }

    /**
     * @dev Get pool statistics
     */
    function getPoolStats(address pool) external view returns (
        bool isWhitelisted,
        uint256 lastHarvest,
        uint256 totalProfit
    ) {
        return (
            whitelistedPools[pool],
            lastHarvestTime[pool],
            poolProfits[pool]
        );
    }

    /**
     * @dev Get contract statistics
     */
    function getStats() external view returns (
        uint256 totalProfit,
        uint256 totalExecutions,
        uint256 failedTxCount,
        bool isEmergencyStop
    ) {
        return (
            totalProfitGenerated,
            totalStrategiesExecuted,
            failedTransactionCount,
            emergencyStop
        );
    }

    // ============ FALLBACK ============

    receive() external payable {
        // Accept ETH for gas refunds
    }
}
