require("@nomicfoundation/hardhat-verify");
require("dotenv").config();

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.19",
    settings: {
      optimizer: {
        enabled: true,
        runs: 1000, // Increased for gas optimization
        details: {
          yul: true,
          yulDetails: {
            stackAllocation: true,
            optimizerSteps: "dhfoDgvulfnTUtnIf"
          }
        }
      },
      viaIR: true // Enable IR optimization for better gas efficiency
    },
  },
  networks: {
    hardhat: {
      forking: {
        url: `https://opt-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw`,
        blockNumber: *********
      },
      chainId: 10,
      accounts: {
        count: 10,
        accountsBalance: "10000000000000000000000" // 10k ETH per account
      }
    },
    optimism: {
      url: `https://opt-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw`,
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 10,
      gasPrice: 1000000, // 0.001 gwei for Optimism
      gas: ******** // 15M gas limit
    },
    ethereum: {
      url: `https://eth-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw`,
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 1,
      gasPrice: ***********, // 20 gwei
      gas: 8000000 // 8M gas limit
    },
    mainnet: {
      url: `https://eth-mainnet.g.alchemy.com/v2/AfgbDuDIx9yi_ynens2Rw`,
      accounts: process.env.PRIVATE_KEY ? [process.env.PRIVATE_KEY] : [],
      chainId: 1,
      gasPrice: ***********, // 50 gwei for mainnet deployment
      gas: 3000000 // 3M gas limit for deployment
    }
  },
  etherscan: {
    apiKey: {
      optimisticEthereum: "W6AMQUTMNXY46M1XH8QI7Z2CJ2CQW8IGGY",
      mainnet: "W6AMQUTMNXY46M1XH8QI7Z2CJ2CQW8IGGY"
    }
  },
  gasReporter: {
    enabled: true,
    currency: 'USD',
    gasPrice: 1 // 1 gwei for Optimism
  },
  mocha: {
    timeout: 300000 // 5 minutes for complex tests
  }
};
