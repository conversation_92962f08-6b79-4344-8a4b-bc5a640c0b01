const { ethers } = require('ethers');
require('dotenv').config();

async function estimateDeploymentCost() {
  try {
    console.log('⛽ ESTIMATING DEPLOYMENT COSTS');
    console.log('═'.repeat(50));
    
    const provider = new ethers.JsonRpcProvider(process.env.MAINNET_RPC_URL);
    const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
    
    const balance = await provider.getBalance(wallet.address);
    const gasPrice = await provider.getFeeData();
    
    console.log('💰 Current Balance:', ethers.formatEther(balance), 'ETH');
    console.log('⛽ Gas Price:', ethers.formatUnits(gasPrice.gasPrice || 0n, 'gwei'), 'gwei');
    console.log('⛽ Max Fee:', ethers.formatUnits(gasPrice.maxFeePerGas || 0n, 'gwei'), 'gwei');
    
    // Estimate gas for each contract deployment
    const contractSizes = {
      'CollateralLoopFarming': 2800000,    // ~2.8M gas
      'LPRewardHarvesting': 2600000,       // ~2.6M gas  
      'MEVSandwichBot': 2400000,           // ~2.4M gas
      'AdvancedFlashLoanManager': 2200000  // ~2.2M gas
    };
    
    let totalGas = 0n;
    console.log('\n📊 GAS ESTIMATES PER CONTRACT:');
    console.log('─'.repeat(40));
    
    for (const [name, gas] of Object.entries(contractSizes)) {
      const gasEstimate = BigInt(gas);
      const cost = gasEstimate * (gasPrice.gasPrice || 0n);
      totalGas += gasEstimate;
      
      console.log(`📦 ${name}:`);
      console.log(`   Gas: ${gasEstimate.toLocaleString()}`);
      console.log(`   Cost: ${ethers.formatEther(cost)} ETH`);
      console.log('');
    }
    
    const totalCost = totalGas * (gasPrice.gasPrice || 0n);
    const totalCostWithBuffer = totalCost * 150n / 100n; // 50% buffer
    
    console.log('💸 TOTAL DEPLOYMENT COST:');
    console.log('─'.repeat(30));
    console.log('⛽ Total Gas:', totalGas.toLocaleString());
    console.log('💰 Base Cost:', ethers.formatEther(totalCost), 'ETH');
    console.log('🛡️ With Buffer:', ethers.formatEther(totalCostWithBuffer), 'ETH');
    
    const canDeploy = balance >= totalCostWithBuffer;
    console.log('\n🚀 DEPLOYMENT FEASIBILITY:');
    console.log('─'.repeat(30));
    
    if (canDeploy) {
      console.log('✅ SUFFICIENT BALANCE FOR DEPLOYMENT');
      console.log('💡 Proceeding with deployment...');
      return true;
    } else {
      const shortfall = totalCostWithBuffer - balance;
      console.log('❌ INSUFFICIENT BALANCE');
      console.log('📈 Shortfall:', ethers.formatEther(shortfall), 'ETH');
      
      // Check if we can deploy with current gas prices (no buffer)
      if (balance >= totalCost) {
        console.log('⚠️  CAN DEPLOY WITHOUT BUFFER (RISKY)');
        console.log('💡 Consider deploying with current low gas prices');
        return 'risky';
      }
      
      return false;
    }
    
  } catch (error) {
    console.error('💥 Error estimating deployment cost:', error.message);
    return false;
  }
}

estimateDeploymentCost().then(result => {
  if (result === true) {
    console.log('\n🎯 Ready to deploy all contracts safely!');
  } else if (result === 'risky') {
    console.log('\n⚠️  Can deploy but with minimal buffer!');
  } else {
    console.log('\n❌ Cannot deploy with current balance');
  }
});
