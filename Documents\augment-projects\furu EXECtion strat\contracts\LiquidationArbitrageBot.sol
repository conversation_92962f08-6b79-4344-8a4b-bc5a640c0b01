// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IAaveV3Pool {
    function liquidationCall(
        address collateralAsset,
        address debtAsset,
        address user,
        uint256 debtToCover,
        bool receiveAToken
    ) external;
    
    function getUserAccountData(address user)
        external
        view
        returns (
            uint256 totalCollateralBase,
            uint256 totalDebtBase,
            uint256 availableBorrowsBase,
            uint256 currentLiquidationThreshold,
            uint256 ltv,
            uint256 healthFactor
        );
}

interface IParaSwapAugustus {
    function multiSwap(
        bytes calldata data
    ) external payable returns (uint256);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params)
        external
        payable
        returns (uint256 amountOut);
}

/**
 * @title LiquidationArbitrageBot
 * @dev Replicates $2M+ profit liquidation strategy from EigenPhi analysis
 * @notice Combines Aave V3 liquidations with ParaSwap routing for maximum profit
 */
contract LiquidationArbitrageBot is ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    // Core protocol addresses
    IBalancerVault public constant BALANCER_VAULT = IBalancerVault(******************************************);
    IAaveV3Pool public constant AAVE_V3_POOL = IAaveV3Pool(******************************************);
    IParaSwapAugustus public constant PARASWAP_AUGUSTUS = IParaSwapAugustus(******************************************);
    IUniswapV3Router public constant UNISWAP_V3_ROUTER = IUniswapV3Router(******************************************);

    // Profit wallet for all earnings
    address public constant PROFIT_WALLET = ******************************************;

    // Minimum profit threshold (in USD, 18 decimals)
    uint256 public constant MIN_PROFIT_USD = 1000 * 1e18; // $1000 minimum

    // Events
    event LiquidationExecuted(
        address indexed user,
        address indexed collateralAsset,
        address indexed debtAsset,
        uint256 debtAmount,
        uint256 profit
    );

    event ArbitrageExecuted(
        address indexed tokenA,
        address indexed tokenB,
        uint256 amountIn,
        uint256 profit
    );

    struct LiquidationParams {
        address user;
        address collateralAsset;
        address debtAsset;
        uint256 debtToCover;
        bytes paraswapData;
        uint256 minProfitUSD;
    }

    struct ArbitrageParams {
        address tokenIn;
        address tokenOut;
        uint256 amountIn;
        bytes paraswapData;
        uint24 uniswapFee;
        bool useParaswap;
    }

    /**
     * @dev Execute liquidation with flash loan and optimal routing
     * @param params Liquidation parameters including user, assets, and routing data
     */
    function executeLiquidation(LiquidationParams calldata params) external onlyOwner nonReentrant {
        require(params.minProfitUSD >= MIN_PROFIT_USD, "Profit below minimum threshold");
        
        // Verify position is liquidatable
        (, , , , , uint256 healthFactor) = AAVE_V3_POOL.getUserAccountData(params.user);
        require(healthFactor < 1e18, "Position not liquidatable");

        // Prepare flash loan
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        tokens[0] = params.debtAsset;
        amounts[0] = params.debtToCover;

        bytes memory userData = abi.encode(
            "LIQUIDATION",
            params.user,
            params.collateralAsset,
            params.debtAsset,
            params.debtToCover,
            params.paraswapData,
            params.minProfitUSD
        );

        BALANCER_VAULT.flashLoan(address(this), tokens, amounts, userData);
    }

    /**
     * @dev Execute pure arbitrage with flash loan
     * @param params Arbitrage parameters including tokens and routing
     */
    function executeArbitrage(ArbitrageParams calldata params) external onlyOwner nonReentrant {
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        tokens[0] = params.tokenIn;
        amounts[0] = params.amountIn;

        bytes memory userData = abi.encode(
            "ARBITRAGE",
            params.tokenIn,
            params.tokenOut,
            params.amountIn,
            params.paraswapData,
            params.uniswapFee,
            params.useParaswap
        );

        BALANCER_VAULT.flashLoan(address(this), tokens, amounts, userData);
    }

    /**
     * @dev Balancer flash loan callback
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external {
        require(msg.sender == address(BALANCER_VAULT), "Unauthorized callback");

        string memory operation = abi.decode(userData, (string));

        if (keccak256(bytes(operation)) == keccak256(bytes("LIQUIDATION"))) {
            _executeLiquidationCallback(tokens[0], amounts[0], userData);
        } else if (keccak256(bytes(operation)) == keccak256(bytes("ARBITRAGE"))) {
            _executeArbitrageCallback(tokens[0], amounts[0], userData);
        }

        // Repay flash loan (Balancer V2 has 0% fee)
        IERC20(tokens[0]).safeTransfer(address(BALANCER_VAULT), amounts[0]);
    }

    /**
     * @dev Internal liquidation execution
     */
    function _executeLiquidationCallback(
        address debtAsset,
        uint256 debtAmount,
        bytes memory userData
    ) internal {
        (
            ,
            address user,
            address collateralAsset,
            ,
            ,
            bytes memory paraswapData,
            uint256 minProfitUSD
        ) = abi.decode(userData, (string, address, address, address, uint256, bytes, uint256));

        uint256 initialBalance = IERC20(debtAsset).balanceOf(address(this));

        // Execute liquidation on Aave V3
        IERC20(debtAsset).safeApprove(address(AAVE_V3_POOL), debtAmount);
        AAVE_V3_POOL.liquidationCall(
            collateralAsset,
            debtAsset,
            user,
            debtAmount,
            false // Receive underlying asset, not aToken
        );

        // Get received collateral
        uint256 collateralReceived = IERC20(collateralAsset).balanceOf(address(this));
        require(collateralReceived > 0, "No collateral received");

        // Swap collateral back to debt asset via ParaSwap for optimal routing
        IERC20(collateralAsset).safeApprove(address(PARASWAP_AUGUSTUS), collateralReceived);
        
        uint256 swapResult = PARASWAP_AUGUSTUS.multiSwap(paraswapData);
        require(swapResult > 0, "ParaSwap failed");

        uint256 finalBalance = IERC20(debtAsset).balanceOf(address(this));
        uint256 profit = finalBalance - initialBalance;

        require(profit > 0, "No profit generated");

        // Convert profit to USD estimate (simplified)
        uint256 profitUSD = profit; // Assume 1:1 for stablecoins, adjust for other assets
        require(profitUSD >= minProfitUSD, "Profit below threshold");

        // Send profit to profit wallet
        IERC20(debtAsset).safeTransfer(PROFIT_WALLET, profit);

        emit LiquidationExecuted(user, collateralAsset, debtAsset, debtAmount, profit);
    }

    /**
     * @dev Internal arbitrage execution
     */
    function _executeArbitrageCallback(
        address tokenIn,
        uint256 amountIn,
        bytes memory userData
    ) internal {
        (
            ,
            ,
            address tokenOut,
            ,
            bytes memory paraswapData,
            uint24 uniswapFee,
            bool useParaswap
        ) = abi.decode(userData, (string, address, address, uint256, bytes, uint24, bool));

        uint256 initialBalance = IERC20(tokenIn).balanceOf(address(this));

        if (useParaswap) {
            // Use ParaSwap for optimal routing
            IERC20(tokenIn).safeApprove(address(PARASWAP_AUGUSTUS), amountIn);
            PARASWAP_AUGUSTUS.multiSwap(paraswapData);
        } else {
            // Use Uniswap V3 direct swap
            IERC20(tokenIn).safeApprove(address(UNISWAP_V3_ROUTER), amountIn);
            
            IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                fee: uniswapFee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amountIn,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });

            UNISWAP_V3_ROUTER.exactInputSingle(params);
        }

        // Calculate and verify profit
        uint256 finalBalance = IERC20(tokenIn).balanceOf(address(this));
        uint256 profit = finalBalance - initialBalance;

        require(profit > 0, "No arbitrage profit");

        // Send profit to profit wallet
        IERC20(tokenIn).safeTransfer(PROFIT_WALLET, profit);

        emit ArbitrageExecuted(tokenIn, tokenOut, amountIn, profit);
    }

    /**
     * @dev Emergency function to recover stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(PROFIT_WALLET, amount);
    }

    /**
     * @dev Check if position is liquidatable
     */
    function isLiquidatable(address user) external view returns (bool, uint256) {
        (, , , , , uint256 healthFactor) = AAVE_V3_POOL.getUserAccountData(user);
        return (healthFactor < 1e18, healthFactor);
    }

    /**
     * @dev Get user position data
     */
    function getUserPositionData(address user) external view returns (
        uint256 totalCollateralBase,
        uint256 totalDebtBase,
        uint256 healthFactor
    ) {
        (totalCollateralBase, totalDebtBase, , , , healthFactor) = AAVE_V3_POOL.getUserAccountData(user);
    }
}
