import { ethers } from "hardhat";

async function main() {
  console.log('🚀 DEPLOYING COLLATERAL LOOP FARMING CONTRACT');
  console.log('═'.repeat(60));

  const [deployer] = await ethers.getSigners();
  
  console.log('💰 DEPLOYMENT DETAILS:');
  console.log(`   Deployer: ${deployer.address}`);
  console.log(`   Profit Wallet: ******************************************`);
  
  const balance = await deployer.provider.getBalance(deployer.address);
  const balanceETH = parseFloat(ethers.formatEther(balance));
  
  console.log(`   Balance: ${balanceETH.toFixed(6)} ETH`);
  console.log(`   Network: ${(await deployer.provider.getNetwork()).name}`);

  // Get gas price
  const feeData = await deployer.provider.getFeeData();
  const gasPrice = feeData.gasPrice || BigInt(0);
  const gasPriceGwei = parseFloat(ethers.formatUnits(gasPrice, 'gwei'));
  
  console.log(`   Gas Price: ${gasPriceGwei.toFixed(2)} gwei`);

  // Estimate deployment cost
  const CollateralLoopFarming = await ethers.getContractFactory("CollateralLoopFarming");
  const deployTx = await CollateralLoopFarming.getDeployTransaction();
  const gasEstimate = await deployer.provider.estimateGas(deployTx);
  const estimatedCost = gasEstimate * gasPrice;
  const estimatedCostETH = parseFloat(ethers.formatEther(estimatedCost));
  
  console.log(`   Estimated Gas: ${gasEstimate.toString()} gas`);
  console.log(`   Estimated Cost: ${estimatedCostETH.toFixed(6)} ETH`);

  // Check if we have enough balance
  if (balance < estimatedCost * BigInt(110) / BigInt(100)) { // 10% buffer
    console.log('\n❌ INSUFFICIENT BALANCE FOR DEPLOYMENT');
    console.log(`   Required: ${(estimatedCostETH * 1.1).toFixed(6)} ETH`);
    console.log(`   Available: ${balanceETH.toFixed(6)} ETH`);
    console.log(`   Shortfall: ${((estimatedCostETH * 1.1) - balanceETH).toFixed(6)} ETH`);
    process.exit(1);
  }

  console.log('\n🏗️  DEPLOYING COLLATERAL LOOP FARMING CONTRACT...');
  console.log('─'.repeat(50));

  console.log('   📋 Contract Features:');
  console.log('     ✅ Aave V3 eMode leveraged farming');
  console.log('     ✅ Compound V3 reward harvesting');
  console.log('     ✅ Balancer V2 flash loans (0% fee)');
  console.log('     ✅ Up to 95% LTV with 5x leverage loops');
  console.log('     ✅ Automatic reward token conversion');
  console.log('     ✅ Profits to ******************************************');
  console.log('     ✅ Circuit breakers and emergency stops');

  // Deploy with conservative gas settings
  const collateralContract = await CollateralLoopFarming.deploy({
    gasPrice: gasPrice, // Use current gas price
    gasLimit: gasEstimate * BigInt(120) / BigInt(100) // 20% buffer
  });

  console.log('\n⏳ Waiting for deployment confirmation...');
  await collateralContract.waitForDeployment();

  const contractAddress = await collateralContract.getAddress();
  const deploymentTx = collateralContract.deploymentTransaction();
  
  if (deploymentTx) {
    const receipt = await deploymentTx.wait();
    const gasCost = receipt ? receipt.gasUsed * (receipt.gasPrice || BigInt(0)) : BigInt(0);
    const gasCostETH = parseFloat(ethers.formatEther(gasCost));

    console.log('\n✅ COLLATERAL LOOP FARMING CONTRACT DEPLOYED!');
    console.log('═'.repeat(60));
    console.log(`🔗 Contract Address: ${contractAddress}`);
    console.log(`🔗 Transaction Hash: ${deploymentTx.hash}`);
    console.log(`⛽ Gas Used: ${receipt?.gasUsed.toString()} gas`);
    console.log(`💰 Deployment Cost: ${gasCostETH.toFixed(6)} ETH`);
    console.log(`👤 Owner: ${deployer.address}`);
    console.log(`💰 Profit Wallet: ******************************************`);

    // Verify contract deployment
    const code = await deployer.provider.getCode(contractAddress);
    if (code === '0x') {
      console.log('❌ Contract deployment failed - no code at address');
      process.exit(1);
    }
    console.log('✅ Contract code verified');

    // Test basic functionality
    console.log('\n🔍 TESTING CONTRACT FUNCTIONALITY...');
    console.log('─'.repeat(40));
    
    try {
      const stats = await collateralContract.getStats();
      console.log('✅ getStats() function works');
      console.log(`   Total Profit: ${ethers.formatUnits(stats.totalProfit, 6)} USDC`);
      console.log(`   Total Executions: ${stats.totalExecutions}`);
      console.log(`   Failed Transactions: ${stats.failedTxCount}`);
      console.log(`   Emergency Stop: ${stats.isEmergencyStop}`);
    } catch (error) {
      console.log('⚠️  getStats() test failed:', (error as Error).message);
    }

    console.log('\n🎯 CONTRACT CAPABILITIES:');
    console.log('─'.repeat(35));
    console.log('   ✅ Flash loan 500-1000 ETH from Balancer V2');
    console.log('   ✅ Create leveraged positions up to 95% LTV');
    console.log('   ✅ Farm rewards from Aave V3 and Compound V3');
    console.log('   ✅ Automatic reward token swapping to WETH');
    console.log('   ✅ Minimum $500 profit per execution');
    console.log('   ✅ Gas optimized (≤1M gas limit)');
    console.log('   ✅ Circuit breakers after 3 failures');

    // Calculate remaining balance
    const finalBalance = await deployer.provider.getBalance(deployer.address);
    const remainingETH = parseFloat(ethers.formatEther(finalBalance));
    
    console.log('\n💰 POST-DEPLOYMENT STATUS:');
    console.log('─'.repeat(30));
    console.log(`   Remaining Balance: ${remainingETH.toFixed(6)} ETH`);
    console.log(`   Can Deploy More: ${remainingETH > 0.01 ? 'Yes' : 'No'}`);

    // Save contract info
    const fs = require('fs');
    const contractInfo = {
      name: 'CollateralLoopFarming',
      address: contractAddress,
      deploymentTx: deploymentTx.hash,
      deployer: deployer.address,
      deploymentCost: gasCostETH,
      gasUsed: receipt?.gasUsed.toString(),
      blockNumber: receipt?.blockNumber,
      timestamp: new Date().toISOString(),
      network: 'mainnet',
      profitWallet: '******************************************'
    };
    
    fs.writeFileSync(
      './deployed-collateral-farming.json', 
      JSON.stringify(contractInfo, null, 2)
    );
    
    console.log('\n📄 Contract info saved to deployed-collateral-farming.json');

    console.log('\n🚀 READY FOR STRATEGY EXECUTION!');
    console.log('💡 Next Steps:');
    console.log('   1. Test with small flash loan amounts first');
    console.log('   2. Monitor for profitable Aave/Compound opportunities');
    console.log('   3. Execute strategies when spreads > 2%');
    console.log('   4. Use profits to deploy remaining contracts');
    
  } else {
    console.log('❌ Deployment transaction not found');
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error('❌ Deployment failed:', error);
    process.exit(1);
  });
