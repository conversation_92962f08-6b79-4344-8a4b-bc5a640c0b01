async function main() {
    console.log("🚀 DEPLOYING COLLATERAL LOOP FARMING CONTRACT - PRODUCTION READY");
    console.log("═══════════════════════════════════════════════════════════════");

    // Get hardhat runtime environment
    const hre = require("hardhat");

    const [deployer] = await hre.ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Profit wallet: ******************************************");
    
    const balance = await hre.ethers.provider.getBalance(deployer.address);
    console.log("Account balance:", hre.ethers.formatEther(balance), "ETH");

    // Check network
    const network = await hre.ethers.provider.getNetwork();
    console.log("Network:", network.name, "Chain ID:", network.chainId);
    
    // Get gas price
    const feeData = await hre.ethers.provider.getFeeData();
    const gasPrice = feeData.gasPrice || BigInt(0);
    const gasPriceGwei = parseFloat(hre.ethers.formatUnits(gasPrice, 'gwei'));
    console.log("Gas Price:", gasPriceGwei.toFixed(2), "gwei");
    
    console.log("\n📋 CONTRACT SPECIFICATIONS:");
    console.log("✅ Strategy: Aave V3 eMode + Compound V3 Leveraged Farming");
    console.log("✅ Flash Loans: Balancer V2 (0% fees) 500-1000 ETH");
    console.log("✅ Leverage: Up to 95% LTV with 5x loops");
    console.log("✅ Rewards: stkAAVE, COMP, OP tokens → WETH");
    console.log("✅ Profit Wallet: ******************************************");
    console.log("✅ Min Profit: $500 per execution");
    console.log("✅ Gas Limit: 1M gas maximum");
    console.log("✅ Circuit Breaker: 3 failed transactions");
    
    // Estimate deployment cost
    console.log("\n⛽ ESTIMATING DEPLOYMENT COST...");
    const CollateralLoopFarming = await hre.ethers.getContractFactory("CollateralLoopFarming");
    
    // Get deployment transaction data
    const deployTx = await CollateralLoopFarming.getDeployTransaction();
    const gasEstimate = await hre.ethers.provider.estimateGas(deployTx);
    const estimatedCost = gasEstimate * gasPrice;
    const estimatedCostETH = parseFloat(hre.ethers.formatEther(estimatedCost));
    
    console.log("Estimated Gas:", gasEstimate.toString());
    console.log("Estimated Cost:", estimatedCostETH.toFixed(6), "ETH");
    
    // Check if we have enough balance
    const balanceETH = parseFloat(hre.ethers.formatEther(balance));
    const requiredETH = estimatedCostETH * 1.2; // 20% buffer
    
    if (balanceETH < requiredETH) {
        console.log("\n❌ INSUFFICIENT BALANCE FOR DEPLOYMENT");
        console.log("Required:", requiredETH.toFixed(6), "ETH");
        console.log("Available:", balanceETH.toFixed(6), "ETH");
        console.log("Shortfall:", (requiredETH - balanceETH).toFixed(6), "ETH");
        
        // Check if we can deploy without buffer
        if (balanceETH >= estimatedCostETH) {
            console.log("\n⚠️  ATTEMPTING DEPLOYMENT WITHOUT BUFFER (RISKY)");
        } else {
            console.log("\n💡 Cannot deploy - need more ETH");
            process.exit(1);
        }
    } else {
        console.log("✅ Sufficient balance for deployment");
    }
    
    console.log("\n🔧 DEPLOYING CONTRACT...");
    
    // Deploy the contract with conservative gas settings
    console.log("⏳ Deploying CollateralLoopFarming contract...");
    const contract = await CollateralLoopFarming.deploy({
        gasPrice: gasPrice,
        gasLimit: gasEstimate * BigInt(120) / BigInt(100) // 20% buffer
    });

    console.log("⏳ Waiting for deployment confirmation...");
    await contract.waitForDeployment();

    const contractAddress = await contract.getAddress();
    console.log("✅ Contract deployed to:", contractAddress);

    // Get deployment transaction details
    const deploymentTx = contract.deploymentTransaction();
    if (deploymentTx) {
        const receipt = await deploymentTx.wait();
        const actualGasUsed = receipt ? receipt.gasUsed : BigInt(0);
        const actualCost = actualGasUsed * (receipt?.gasPrice || gasPrice);
        const actualCostETH = parseFloat(hre.ethers.formatEther(actualCost));
        
        console.log("⛽ Actual Gas Used:", actualGasUsed.toString());
        console.log("💰 Actual Cost:", actualCostETH.toFixed(6), "ETH");
        console.log("🔗 Transaction Hash:", deploymentTx.hash);
    }

    // Verify deployment
    console.log("\n🔍 VERIFYING DEPLOYMENT...");

    // Check contract code
    const code = await hre.ethers.provider.getCode(contractAddress);
    if (code === "0x") {
        throw new Error("❌ Contract deployment failed - no code at address");
    }
    console.log("✅ Contract code verified");
    
    // Check constants and basic functionality
    try {
        const profitWallet = await contract.PROFIT_WALLET();
        const balancerVault = await contract.BALANCER_VAULT();
        const minProfit = await contract.MIN_PROFIT_THRESHOLD();
        const maxLTV = await contract.MAX_LTV_RATIO();
        
        console.log("✅ Profit wallet:", profitWallet);
        console.log("✅ Balancer Vault:", balancerVault);
        console.log("✅ Min profit threshold:", hre.ethers.formatUnits(minProfit, 6), "USDC");
        console.log("✅ Max LTV ratio:", maxLTV.toString() + "%");
        
        if (profitWallet !== "******************************************") {
            throw new Error("❌ Profit wallet mismatch");
        }
        
        if (balancerVault !== "******************************************") {
            throw new Error("❌ Balancer Vault address mismatch");
        }
        
    } catch (error) {
        console.error("❌ Contract verification failed:", error.message);
        throw error;
    }
    
    // Test contract functions
    console.log("\n🧪 TESTING CONTRACT FUNCTIONS...");
    
    try {
        const stats = await contract.getStats();
        console.log("✅ getStats() working:");
        console.log("   Total profit:", hre.ethers.formatUnits(stats.totalProfit, 6), "USDC");
        console.log("   Total executions:", stats.totalExecutions.toString());
        console.log("   Failed transactions:", stats.failedTxCount.toString());
        console.log("   Emergency stop:", stats.isEmergencyStop);
        
        // Check owner
        const owner = await contract.owner();
        console.log("✅ Contract owner:", owner);
        
        if (owner !== deployer.address) {
            throw new Error("❌ Owner mismatch");
        }
        
    } catch (error) {
        console.error("❌ Contract function test failed:", error.message);
        throw error;
    }
    
    // Check final balance
    const finalBalance = await hre.ethers.provider.getBalance(deployer.address);
    const finalBalanceETH = parseFloat(hre.ethers.formatEther(finalBalance));
    const deploymentCost = balanceETH - finalBalanceETH;
    
    console.log("\n💰 DEPLOYMENT COST SUMMARY:");
    console.log("Initial balance:", balanceETH.toFixed(6), "ETH");
    console.log("Final balance:", finalBalanceETH.toFixed(6), "ETH");
    console.log("Total cost:", deploymentCost.toFixed(6), "ETH");
    
    console.log("\n🎯 DEPLOYMENT SUMMARY:");
    console.log("═══════════════════════════════════════════════════════════════");
    console.log("📍 Contract Address:", contractAddress);
    console.log("🏦 Profit Wallet: ******************************************");
    console.log("💰 Deployment Cost:", deploymentCost.toFixed(6), "ETH");
    console.log("🔧 Owner:", deployer.address);
    console.log("🌐 Network:", network.name);
    console.log("⛽ Gas Price:", gasPriceGwei.toFixed(2), "gwei");
    
    console.log("\n📋 STRATEGY CAPABILITIES:");
    console.log("1. ✅ Flash loan 500-1000 ETH from Balancer V2");
    console.log("2. ✅ Create leveraged positions on Aave V3 eMode");
    console.log("3. ✅ Farm rewards from Compound V3");
    console.log("4. ✅ Automatic reward token conversion to WETH");
    console.log("5. ✅ Send profits to designated wallet");
    console.log("6. ✅ Circuit breakers and emergency stops");
    
    console.log("\n📋 NEXT STEPS:");
    console.log("1. ✅ Contract deployed and verified");
    console.log("2. 🔄 Monitor Aave/Compound rate spreads");
    console.log("3. 💰 Execute when spread > 2% and profit > $500");
    console.log("4. 📊 Use profits to deploy remaining contracts");
    
    console.log("\n🚀 COLLATERAL LOOP FARMING CONTRACT IS LIVE!");
    
    // Save deployment info
    const deploymentInfo = {
        contractName: "CollateralLoopFarming",
        contractAddress: contractAddress,
        deployer: deployer.address,
        network: network.name,
        chainId: network.chainId.toString(),
        profitWallet: "******************************************",
        deploymentCost: deploymentCost.toFixed(6),
        gasPrice: gasPriceGwei.toFixed(2),
        deploymentTime: new Date().toISOString(),
        transactionHash: deploymentTx ? deploymentTx.hash : null
    };
    
    const fs = require('fs');
    fs.writeFileSync(
        'deployed-collateral-farming.json', 
        JSON.stringify(deploymentInfo, null, 2)
    );
    
    console.log("💾 Deployment info saved to deployed-collateral-farming.json");
    
    return {
        contract: contract,
        address: contractAddress,
        deployer: deployer.address,
        cost: deploymentCost
    };
}

// Execute deployment
main()
    .then((result) => {
        console.log("\n🎉 COLLATERAL LOOP FARMING DEPLOYMENT SUCCESSFUL!");
        console.log("Contract ready for profitable strategy execution!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n💥 DEPLOYMENT FAILED:");
        console.error(error);
        process.exit(1);
    });
