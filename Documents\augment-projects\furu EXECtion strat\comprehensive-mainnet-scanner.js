#!/usr/bin/env node

/**
 * COMPREHENSIVE REAL-TIME MAINNET OPPORTUNITY SCANNER
 * 
 * Executes all implemented opportunity detection systems to identify
 * actual profitable arbitrage opportunities on Ethereum mainnet using
 * live blockchain data via Alchemy RPC.
 * 
 * Requirements:
 * - Real mainnet data only (no mock/simulated data)
 * - All scanner types: Cross-DEX, Liquidation, Yield Arbitrage, Flash Loan Refinancing
 * - Minimum profitability thresholds with real market validation
 * - Actual gas cost calculations and net profit verification
 * - Execution readiness with specific parameters
 */

require('dotenv').config();
const { ethers } = require('ethers');
const { MultiStrategyScanner } = require('./alpha-scanner/core/multi-strategy-scanner');
const { Web3Utils } = require('./alpha-scanner/utils/web3');
const fs = require('fs');
const path = require('path');

class ComprehensiveMainnetScanner {
  constructor() {
    this.startTime = Date.now();
    this.results = {
      scanId: `mainnet_scan_${this.startTime}`,
      timestamp: this.startTime,
      chain: 'ethereum',
      scanDuration: 0,
      totalOpportunities: 0,
      totalProfitUSD: 0,
      scannerResults: {},
      topOpportunities: [],
      executionReadyOpportunities: [],
      marketConditions: {},
      gasAnalysis: {},
      errors: []
    };

    // Initialize with Ethereum mainnet
    this.chainName = 'ethereum';
    this.web3 = new Web3Utils(this.chainName);
    
    // Verify API key configuration
    if (!process.env.ALCHEMY_API_KEY || !process.env.MAINNET_RPC_URL) {
      throw new Error('❌ Missing Alchemy API configuration in .env file');
    }

    this.log = (message, level = 'INFO') => {
      const timestamp = new Date().toISOString();
      const logMessage = `${timestamp} [${level}] ${message}`;
      console.log(logMessage);
      
      // Also write to results for comprehensive reporting
      if (level === 'ERROR') {
        this.results.errors.push({ timestamp, message });
      }
    };

    this.log('🚀 COMPREHENSIVE MAINNET OPPORTUNITY SCANNER INITIALIZED');
    this.log(`Chain: Ethereum Mainnet`);
    this.log(`RPC: ${process.env.MAINNET_RPC_URL.substring(0, 50)}...`);
    this.log(`Scan ID: ${this.results.scanId}`);
  }

  // Execute comprehensive scan across all strategies
  async executeComprehensiveScan() {
    this.log('═══════════════════════════════════════════════════════════');
    this.log('🔍 STARTING COMPREHENSIVE REAL-TIME MAINNET SCAN');
    this.log('═══════════════════════════════════════════════════════════');

    try {
      // Step 1: Verify network connectivity and get current market conditions
      await this.verifyNetworkAndMarketConditions();

      // Step 2: Execute all scanner strategies
      await this.executeAllScannerStrategies();

      // Step 3: Analyze and filter results
      await this.analyzeAndFilterResults();

      // Step 4: Validate execution readiness
      await this.validateExecutionReadiness();

      // Step 5: Generate comprehensive report
      await this.generateComprehensiveReport();

      this.log('✅ COMPREHENSIVE SCAN COMPLETED SUCCESSFULLY');
      return this.results;

    } catch (error) {
      this.log(`❌ COMPREHENSIVE SCAN FAILED: ${error.message}`, 'ERROR');
      this.results.scanDuration = (Date.now() - this.startTime) / 1000;
      throw error;
    }
  }

  // Verify network connectivity and get current market conditions
  async verifyNetworkAndMarketConditions() {
    this.log('🌐 Verifying network connectivity and market conditions...');

    try {
      // Test network connection
      const currentBlock = await this.web3.getCurrentBlock();
      const gasPrice = await this.web3.getGasPrice();
      
      this.results.marketConditions = {
        currentBlock,
        blockTimestamp: Date.now(),
        gasPrice: {
          standard: Number(ethers.formatUnits(gasPrice.gasPrice, 'gwei')),
          fast: Number(ethers.formatUnits(gasPrice.gasPrice * BigInt(120) / BigInt(100), 'gwei')),
          instant: Number(ethers.formatUnits(gasPrice.gasPrice * BigInt(150) / BigInt(100), 'gwei'))
        }
      };

      // Get ETH price for USD calculations
      const ethPrice = await this.getETHPriceUSD();
      this.results.marketConditions.ethPriceUSD = ethPrice;

      this.log(`✅ Network connected - Block: ${currentBlock}`);
      this.log(`⛽ Gas Price: ${this.results.marketConditions.gasPrice.standard.toFixed(1)} gwei`);
      this.log(`💰 ETH Price: $${ethPrice.toFixed(2)}`);

      // Validate gas costs are reasonable for profitable execution
      if (this.results.marketConditions.gasPrice.standard > 100) {
        this.log(`⚠️ HIGH GAS WARNING: ${this.results.marketConditions.gasPrice.standard.toFixed(1)} gwei - May impact profitability`, 'WARN');
      }

    } catch (error) {
      this.log(`❌ Network verification failed: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  // Execute all scanner strategies
  async executeAllScannerStrategies() {
    this.log('\n🔄 Executing all scanner strategies...');

    try {
      // Initialize multi-strategy scanner for Ethereum mainnet
      const multiScanner = new MultiStrategyScanner('ethereum');
      
      this.log('📊 Scanner Status:');
      const status = multiScanner.getStatus();
      this.log(`   Strategies: ${status.strategies.join(', ')}`);
      this.log(`   Min Profit: $${status.minProfitThreshold.toLocaleString()}`);

      // Execute all strategies
      const scanResults = await multiScanner.executeAllStrategies();
      
      this.results.scannerResults = scanResults;
      this.results.totalOpportunities = scanResults.totalOpportunities;
      this.results.totalProfitUSD = scanResults.totalProfit;

      this.log(`✅ All scanners executed successfully`);
      this.log(`📈 Total opportunities found: ${this.results.totalOpportunities}`);
      this.log(`💰 Total profit potential: $${this.results.totalProfitUSD.toFixed(2)}`);

    } catch (error) {
      this.log(`❌ Scanner execution failed: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  // Get current ETH price in USD
  async getETHPriceUSD() {
    try {
      // Use Chainlink ETH/USD price feed on mainnet
      const chainlinkABI = ['function latestRoundData() view returns (uint80, int256, uint256, uint256, uint80)'];
      const ethUsdFeed = '******************************************';
      const priceFeed = new ethers.Contract(ethUsdFeed, chainlinkABI, this.web3.provider);
      
      const [, price] = await priceFeed.latestRoundData();
      return Number(ethers.formatUnits(price, 8)); // Chainlink uses 8 decimals
    } catch (error) {
      this.log(`⚠️ Could not fetch ETH price from Chainlink, using fallback: ${error.message}`, 'WARN');
      return 3500; // Fallback price
    }
  }

  // Analyze and filter results based on profitability thresholds
  async analyzeAndFilterResults() {
    this.log('\n📊 Analyzing and filtering results...');

    try {
      const opportunities = this.results.scannerResults.opportunities || [];
      
      if (opportunities.length === 0) {
        this.log('❌ No opportunities found to analyze');
        return;
      }

      // Apply minimum profitability thresholds based on strategy type
      const thresholds = {
        cross_dex_arbitrage: 500,    // $500 minimum (0.3% spread requirement)
        aave_v3_liquidation: 500,    // $500 minimum (5% liquidation bonus)
        compound_v3_liquidation: 500, // $500 minimum
        yield_arbitrage: 1000,       // $1000 minimum (0.5% rate difference)
        flash_loan_refinance: 1000   // $1000 minimum (2% rate improvement)
      };

      const filteredOpportunities = opportunities.filter(opp => {
        const threshold = thresholds[opp.strategyName] || 500;
        return opp.profitUSD >= threshold;
      });

      // Sort by profit descending
      filteredOpportunities.sort((a, b) => b.profitUSD - a.profitUSD);

      this.results.topOpportunities = filteredOpportunities.slice(0, 10);
      
      this.log(`✅ Filtered ${filteredOpportunities.length}/${opportunities.length} opportunities meeting thresholds`);
      
      if (filteredOpportunities.length > 0) {
        this.log('🏆 TOP OPPORTUNITIES:');
        filteredOpportunities.slice(0, 5).forEach((opp, i) => {
          this.log(`   ${i + 1}. ${opp.strategyName}: $${opp.profitUSD.toFixed(2)} profit`);
          if (opp.pair) this.log(`      Pair: ${opp.pair}`);
          if (opp.protocol) this.log(`      Protocol: ${opp.protocol}`);
          this.log(`      Gas: ${opp.gasEstimate?.toLocaleString() || 'N/A'}`);
        });
      }

    } catch (error) {
      this.log(`❌ Results analysis failed: ${error.message}`, 'ERROR');
    }
  }

  // Validate execution readiness for top opportunities
  async validateExecutionReadiness() {
    this.log('\n🔧 Validating execution readiness...');

    try {
      const executionReady = [];
      
      for (const opportunity of this.results.topOpportunities) {
        const validation = await this.validateOpportunityExecution(opportunity);
        if (validation.isExecutionReady) {
          executionReady.push({
            ...opportunity,
            executionValidation: validation
          });
        }
      }

      this.results.executionReadyOpportunities = executionReady;
      
      this.log(`✅ ${executionReady.length}/${this.results.topOpportunities.length} opportunities are execution-ready`);
      
      if (executionReady.length > 0) {
        this.log('🚀 EXECUTION-READY OPPORTUNITIES:');
        executionReady.forEach((opp, i) => {
          this.log(`   ${i + 1}. ${opp.strategyName}: $${opp.profitUSD.toFixed(2)}`);
          this.log(`      Flash Loan: ${opp.flashLoanAmount} ETH`);
          this.log(`      Net Profit: $${opp.executionValidation.netProfitAfterGas.toFixed(2)}`);
          this.log(`      Profit Margin: ${opp.executionValidation.profitMarginPercent.toFixed(1)}%`);
        });
      }

    } catch (error) {
      this.log(`❌ Execution validation failed: ${error.message}`, 'ERROR');
    }
  }

  // Validate individual opportunity for execution readiness
  async validateOpportunityExecution(opportunity) {
    try {
      const gasPrice = this.results.marketConditions.gasPrice.fast;
      const ethPrice = this.results.marketConditions.ethPriceUSD;

      // Calculate actual gas cost in USD
      const gasCostETH = (opportunity.gasEstimate * gasPrice * 1e9) / 1e18;
      const gasCostUSD = gasCostETH * ethPrice;

      // Calculate net profit after gas
      const netProfitAfterGas = opportunity.profitUSD - gasCostUSD;
      const profitMarginPercent = (netProfitAfterGas / opportunity.profitUSD) * 100;

      // Execution readiness criteria
      const isExecutionReady =
        netProfitAfterGas >= 100 &&           // Minimum $100 net profit
        profitMarginPercent >= 20 &&          // Minimum 20% profit margin
        opportunity.gasEstimate <= 1000000 &&  // Maximum 1M gas
        gasPrice <= 200;                       // Maximum 200 gwei gas price

      return {
        isExecutionReady,
        gasCostUSD,
        netProfitAfterGas,
        profitMarginPercent,
        gasPrice,
        ethPrice,
        validationNotes: isExecutionReady ?
          'All criteria met for execution' :
          'Does not meet execution criteria'
      };

    } catch (error) {
      return {
        isExecutionReady: false,
        error: error.message
      };
    }
  }

  // Generate comprehensive report
  async generateComprehensiveReport() {
    this.log('\n📋 Generating comprehensive report...');

    try {
      this.results.scanDuration = (Date.now() - this.startTime) / 1000;

      // Generate detailed gas analysis
      this.results.gasAnalysis = this.generateGasAnalysis();

      // Save results to file
      const reportPath = await this.saveResultsToFile();

      // Display summary
      this.displayFinalSummary();

      this.log(`💾 Comprehensive report saved: ${reportPath}`);

    } catch (error) {
      this.log(`❌ Report generation failed: ${error.message}`, 'ERROR');
    }
  }

  // Generate gas analysis for all opportunities
  generateGasAnalysis() {
    const opportunities = this.results.topOpportunities;
    const gasPrice = this.results.marketConditions.gasPrice.standard;
    const ethPrice = this.results.marketConditions.ethPriceUSD;

    if (opportunities.length === 0) {
      return { message: 'No opportunities to analyze' };
    }

    const gasEstimates = opportunities.map(opp => opp.gasEstimate).filter(Boolean);
    const gasCosts = gasEstimates.map(gas => (gas * gasPrice * 1e9 / 1e18) * ethPrice);

    return {
      averageGasEstimate: gasEstimates.reduce((a, b) => a + b, 0) / gasEstimates.length,
      averageGasCostUSD: gasCosts.reduce((a, b) => a + b, 0) / gasCosts.length,
      maxGasCostUSD: Math.max(...gasCosts),
      minGasCostUSD: Math.min(...gasCosts),
      currentGasPriceGwei: gasPrice,
      ethPriceUSD: ethPrice,
      profitableAtCurrentGas: opportunities.filter(opp => {
        const gasCostUSD = (opp.gasEstimate * gasPrice * 1e9 / 1e18) * ethPrice;
        return (opp.profitUSD - gasCostUSD) > 100;
      }).length
    };
  }

  // Save results to JSON file
  async saveResultsToFile() {
    try {
      const filename = `mainnet_opportunities_${this.startTime}.json`;
      const filepath = path.join(__dirname, filename);

      const reportData = {
        ...this.results,
        generatedAt: new Date().toISOString(),
        apiConfiguration: {
          alchemyConfigured: !!process.env.ALCHEMY_API_KEY,
          mainnetRpcConfigured: !!process.env.MAINNET_RPC_URL,
          etherscanConfigured: !!process.env.ETHERSCAN_API_KEY
        }
      };

      fs.writeFileSync(filepath, JSON.stringify(reportData, null, 2));
      return filename;
    } catch (error) {
      this.log(`❌ Failed to save results: ${error.message}`, 'ERROR');
      return null;
    }
  }

  // Display final summary
  displayFinalSummary() {
    this.log('\n═══════════════════════════════════════════════════════════');
    this.log('📊 COMPREHENSIVE MAINNET SCAN SUMMARY');
    this.log('═══════════════════════════════════════════════════════════');

    this.log(`🕐 Scan Duration: ${this.results.scanDuration.toFixed(1)} seconds`);
    this.log(`🔗 Block Number: ${this.results.marketConditions.currentBlock}`);
    this.log(`⛽ Gas Price: ${this.results.marketConditions.gasPrice.standard.toFixed(1)} gwei`);
    this.log(`💰 ETH Price: $${this.results.marketConditions.ethPriceUSD.toFixed(2)}`);

    this.log(`\n📈 OPPORTUNITY SUMMARY:`);
    this.log(`Total Opportunities Found: ${this.results.totalOpportunities}`);
    this.log(`Total Profit Potential: $${this.results.totalProfitUSD.toFixed(2)}`);
    this.log(`Execution-Ready Opportunities: ${this.results.executionReadyOpportunities.length}`);

    if (this.results.executionReadyOpportunities.length > 0) {
      const totalExecutableProfit = this.results.executionReadyOpportunities
        .reduce((sum, opp) => sum + opp.executionValidation.netProfitAfterGas, 0);
      this.log(`Executable Profit (Net): $${totalExecutableProfit.toFixed(2)}`);
    }

    // Strategy breakdown
    if (this.results.scannerResults.strategyBreakdown) {
      this.log(`\n🎯 STRATEGY BREAKDOWN:`);
      Object.entries(this.results.scannerResults.strategyBreakdown).forEach(([strategy, data]) => {
        if (!data.error) {
          this.log(`${strategy}: ${data.profitable}/${data.totalFound} profitable ($${data.totalProfit.toFixed(2)})`);
        } else {
          this.log(`${strategy}: ERROR - ${data.error}`);
        }
      });
    }

    // Gas analysis
    if (this.results.gasAnalysis.averageGasCostUSD) {
      this.log(`\n⛽ GAS ANALYSIS:`);
      this.log(`Average Gas Cost: $${this.results.gasAnalysis.averageGasCostUSD.toFixed(2)}`);
      this.log(`Profitable at Current Gas: ${this.results.gasAnalysis.profitableAtCurrentGas}/${this.results.topOpportunities.length}`);
    }

    // Execution instructions
    if (this.results.executionReadyOpportunities.length > 0) {
      this.log(`\n🚀 EXECUTION INSTRUCTIONS:`);
      this.log(`Ready-to-execute opportunities found!`);
      this.log(`Use the following parameters for execution:`);

      this.results.executionReadyOpportunities.slice(0, 3).forEach((opp, i) => {
        this.log(`\n${i + 1}. ${opp.strategyName.toUpperCase()}:`);
        this.log(`   Flash Loan Amount: ${opp.flashLoanAmount} ETH`);
        this.log(`   Expected Profit: $${opp.executionValidation.netProfitAfterGas.toFixed(2)}`);
        this.log(`   Gas Limit: ${opp.gasEstimate.toLocaleString()}`);
        this.log(`   Profit Wallet: ${process.env.PROFIT_WALLET_ADDRESS}`);

        if (opp.pair) {
          this.log(`   Trading Pair: ${opp.pair}`);
        }
        if (opp.buyDex && opp.sellDex) {
          this.log(`   Route: Buy ${opp.buyDex} → Sell ${opp.sellDex}`);
        }
        if (opp.userAddress) {
          this.log(`   Target Address: ${opp.userAddress}`);
        }
      });
    } else {
      this.log(`\n❌ NO EXECUTION-READY OPPORTUNITIES`);
      this.log(`Reasons opportunities may not be execution-ready:`);
      this.log(`• Gas costs too high relative to profit`);
      this.log(`• Profit margins below 20% threshold`);
      this.log(`• Gas estimates exceed 1M limit`);
      this.log(`• Current gas price above 200 gwei`);
      this.log(`• Market conditions not favorable`);
    }

    this.log('\n═══════════════════════════════════════════════════════════');
  }
}

// Main execution function
async function main() {
  try {
    console.log('🚀 Starting Comprehensive Mainnet Opportunity Scanner...\n');

    const scanner = new ComprehensiveMainnetScanner();
    const results = await scanner.executeComprehensiveScan();

    console.log('\n✅ Scan completed successfully!');
    console.log(`📊 Results saved to: mainnet_opportunities_${results.timestamp}.json`);

    // Exit with appropriate code
    process.exit(results.executionReadyOpportunities.length > 0 ? 0 : 1);

  } catch (error) {
    console.error('\n❌ Scanner failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

module.exports = { ComprehensiveMainnetScanner };
