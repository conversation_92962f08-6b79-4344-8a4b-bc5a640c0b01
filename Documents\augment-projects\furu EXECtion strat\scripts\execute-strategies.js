const { ethers } = require("hardhat");
const { parseEther, formatEther, parseUnits, formatUnits } = ethers;

// Contract ABI (simplified for execution)
const contractABI = [
    "function executeCrossDexArbitrage(address tokenA, address tokenB, uint256 amountIn, bool buyFromUniswap) external",
    "function executeLiquidation(address user, address collateralAsset, address debtAsset, uint256 debtToCover) external",
    "function executeYieldArbitrage(address asset, uint256 amount, bool borrowFromAave) external",
    "function executeFlashLoanRefinancing(address user, address asset, uint256 amount, uint256 currentRate, uint256 newRate) external",
    "function getStats() external view returns (uint256, uint256, uint256, bool)",
    "function addAuthorizedCaller(address caller) external",
    "function emergencyPause() external",
    "function emergencyUnpause() external"
];

// Token addresses on Optimism
const TOKENS = {
    WETH: "******************************************",
    USDC: "******************************************",
    DAI: "******************************************",
    USDT: "******************************************"
};

async function main() {
    console.log("🚀 ULTIMATE ARBITRAGE BOT - STRATEGY EXECUTION");
    console.log("═══════════════════════════════════════════════════════════");
    
    // Load deployment info
    const fs = require('fs');
    let deploymentInfo;
    try {
        deploymentInfo = JSON.parse(fs.readFileSync('deployment-info.json', 'utf8'));
    } catch (error) {
        throw new Error("❌ deployment-info.json not found. Deploy contract first.");
    }
    
    const [executor] = await ethers.getSigners();
    console.log("Executing with account:", executor.address);
    console.log("Account balance:", formatEther(await executor.provider.getBalance(executor.address)), "ETH");
    
    // Connect to deployed contract
    const contract = new ethers.Contract(deploymentInfo.contractAddress, contractABI, executor);
    console.log("✅ Connected to contract:", deploymentInfo.contractAddress);
    
    // Check contract stats
    const stats = await contract.getStats();
    console.log("📊 Current Stats:");
    console.log("   Total Profit:", formatUnits(stats[0], 6), "USDC");
    console.log("   Total Transactions:", stats[1].toString());
    console.log("   Failed Transactions:", stats[2].toString());
    console.log("   Circuit Breaker:", stats[3] ? "TRIPPED" : "OK");
    
    console.log("\n🎯 TESTING ARBITRAGE STRATEGIES...");
    
    // Strategy 1: Cross-DEX Arbitrage
    await testCrossDexArbitrage(contract);
    
    // Strategy 2: Liquidation (if opportunities exist)
    await testLiquidation(contract);
    
    // Strategy 3: Yield Arbitrage
    await testYieldArbitrage(contract);
    
    // Strategy 4: Flash Loan Refinancing
    await testFlashLoanRefinancing(contract);
    
    // Final stats
    const finalStats = await contract.getStats();
    console.log("\n📊 FINAL STATS:");
    console.log("═══════════════════════════════════════════════════════════");
    console.log("Total Profit Generated:", formatUnits(finalStats[0], 6), "USDC");
    console.log("Total Transactions:", finalStats[1].toString());
    console.log("Failed Transactions:", finalStats[2].toString());
    console.log("Success Rate:", ((finalStats[1] - finalStats[2]) * 100n / (finalStats[1] || 1n)).toString() + "%");
    
    if (finalStats[0] > 0) {
        console.log("\n🎉 PROFIT GENERATED! Contract is working!");
        console.log("💰 Check profit wallet: ******************************************");
    } else {
        console.log("\n💡 No profits yet - this is normal for testing");
        console.log("🔄 Contract will find opportunities when market conditions are favorable");
    }
}

async function testCrossDexArbitrage(contract) {
    console.log("\n1️⃣ TESTING CROSS-DEX ARBITRAGE...");
    
    try {
        // Test WETH/USDC arbitrage with small amount
        const testAmount = parseEther("0.01"); // 0.01 ETH
        
        console.log("   🔍 Testing WETH/USDC arbitrage...");
        console.log("   💰 Amount:", formatEther(testAmount), "ETH");
        
        // Estimate gas first
        try {
            const gasEstimate = await contract.executeCrossDexArbitrage.estimateGas(
                TOKENS.WETH,
                TOKENS.USDC,
                testAmount,
                true // Buy from Uniswap
            );
            console.log("   ⛽ Estimated gas:", gasEstimate.toString());
            
            if (gasEstimate > 1000000n) {
                console.log("   ⚠️ Gas estimate too high, skipping execution");
                return;
            }
        } catch (error) {
            console.log("   ⚠️ Gas estimation failed:", error.message);
            console.log("   💡 This likely means no profitable arbitrage opportunity exists");
            return;
        }
        
        // Execute if gas estimate is reasonable
        const tx = await contract.executeCrossDexArbitrage(
            TOKENS.WETH,
            TOKENS.USDC,
            testAmount,
            true,
            {
                gasLimit: 800000 // Conservative gas limit
            }
        );
        
        console.log("   📝 Transaction hash:", tx.hash);
        const receipt = await tx.wait();
        console.log("   ✅ Cross-DEX arbitrage executed successfully");
        console.log("   ⛽ Gas used:", receipt.gasUsed.toString());
        
    } catch (error) {
        console.log("   ❌ Cross-DEX arbitrage failed:", error.message);
        if (error.message.includes("No arbitrage opportunity")) {
            console.log("   💡 No profitable opportunity found - this is normal");
        } else if (error.message.includes("Profit below threshold")) {
            console.log("   💡 Opportunity exists but below $500 threshold");
        }
    }
}

async function testLiquidation(contract) {
    console.log("\n2️⃣ TESTING LIQUIDATION...");
    
    try {
        // Test liquidation with example addresses (these may not be liquidatable)
        const testUser = "******************************************";
        const collateralAsset = TOKENS.WETH;
        const debtAsset = TOKENS.USDC;
        const debtToCover = parseUnits("1000", 6); // 1000 USDC
        
        console.log("   🔍 Testing liquidation opportunity...");
        console.log("   👤 User:", testUser);
        console.log("   💰 Debt to cover:", formatUnits(debtToCover, 6), "USDC");
        
        // Estimate gas
        try {
            const gasEstimate = await contract.executeLiquidation.estimateGas(
                testUser,
                collateralAsset,
                debtAsset,
                debtToCover
            );
            console.log("   ⛽ Estimated gas:", gasEstimate.toString());
        } catch (error) {
            console.log("   ⚠️ Gas estimation failed:", error.message);
            console.log("   💡 User likely not liquidatable or insufficient collateral");
            return;
        }
        
        const tx = await contract.executeLiquidation(
            testUser,
            collateralAsset,
            debtAsset,
            debtToCover,
            {
                gasLimit: 800000
            }
        );
        
        console.log("   📝 Transaction hash:", tx.hash);
        const receipt = await tx.wait();
        console.log("   ✅ Liquidation executed successfully");
        console.log("   ⛽ Gas used:", receipt.gasUsed.toString());
        
    } catch (error) {
        console.log("   ❌ Liquidation failed:", error.message);
        if (error.message.includes("User not liquidatable")) {
            console.log("   💡 No liquidatable users found - this is normal");
        }
    }
}

async function testYieldArbitrage(contract) {
    console.log("\n3️⃣ TESTING YIELD ARBITRAGE...");
    
    try {
        const testAmount = parseUnits("1000", 6); // 1000 USDC
        
        console.log("   🔍 Testing yield arbitrage...");
        console.log("   💰 Amount:", formatUnits(testAmount, 6), "USDC");
        
        const tx = await contract.executeYieldArbitrage(
            TOKENS.USDC,
            testAmount,
            true, // Borrow from Aave
            {
                gasLimit: 600000
            }
        );
        
        console.log("   📝 Transaction hash:", tx.hash);
        const receipt = await tx.wait();
        console.log("   ✅ Yield arbitrage executed successfully");
        console.log("   ⛽ Gas used:", receipt.gasUsed.toString());
        
    } catch (error) {
        console.log("   ❌ Yield arbitrage failed:", error.message);
        if (error.message.includes("not profitable")) {
            console.log("   💡 Rate difference too small for profitable arbitrage");
        }
    }
}

async function testFlashLoanRefinancing(contract) {
    console.log("\n4️⃣ TESTING FLASH LOAN REFINANCING...");
    
    try {
        const testUser = "0x8d1Fb1241880d2A30d9d2762C8dB643a5145B21B";
        const testAmount = parseUnits("5000", 6); // 5000 USDC
        const currentRate = 800; // 8% current rate
        const newRate = 600; // 6% new rate
        
        console.log("   🔍 Testing refinancing opportunity...");
        console.log("   👤 User:", testUser);
        console.log("   💰 Amount:", formatUnits(testAmount, 6), "USDC");
        console.log("   📈 Rate improvement: 8% → 6%");
        
        const tx = await contract.executeFlashLoanRefinancing(
            testUser,
            TOKENS.USDC,
            testAmount,
            currentRate,
            newRate,
            {
                gasLimit: 700000
            }
        );
        
        console.log("   📝 Transaction hash:", tx.hash);
        const receipt = await tx.wait();
        console.log("   ✅ Flash loan refinancing executed successfully");
        console.log("   ⛽ Gas used:", receipt.gasUsed.toString());
        
    } catch (error) {
        console.log("   ❌ Flash loan refinancing failed:", error.message);
        if (error.message.includes("No rate improvement")) {
            console.log("   💡 No beneficial rate difference found");
        }
    }
}

// Execute if called directly
if (require.main === module) {
    main()
        .then(() => {
            console.log("\n🎉 STRATEGY TESTING COMPLETED!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("\n💥 STRATEGY TESTING FAILED:");
            console.error(error);
            process.exit(1);
        });
}

module.exports = main;
