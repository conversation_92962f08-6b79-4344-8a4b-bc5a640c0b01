const hre = require('hardhat');
const { ethers } = hre;
require('dotenv').config();

async function deployMinimal() {
  console.log('\n🚀 MINIMAL DEPLOYMENT - COLLATERAL LOOP FARMING');
  console.log('═'.repeat(60));

  const [deployer] = await ethers.getSigners();
  const deployerAddress = await deployer.getAddress();
  const initialBalance = await ethers.provider.getBalance(deployerAddress);

  console.log(`📋 Deployer: ${deployerAddress}`);
  console.log(`💰 Initial Balance: ${ethers.formatEther(initialBalance)} ETH`);
  console.log(`🎯 Profit Wallet: ${process.env.PROFIT_WALLET_ADDRESS}`);

  const gasPrice = await ethers.provider.getFeeData();
  console.log(`⛽ Gas Price: ${ethers.formatUnits(gasPrice.gasPrice || 0n, 'gwei')} gwei`);

  try {
    // Deploy CollateralLoopFarming first (most profitable strategy)
    console.log('\n📦 DEPLOYING COLLATERAL LOOP FARMING CONTRACT...');
    console.log('─'.repeat(50));
    
    const CollateralLoopFarming = await ethers.getContractFactory('CollateralLoopFarming');
    
    // Estimate gas first
    const deployTx = await CollateralLoopFarming.getDeployTransaction();
    const gasEstimate = await ethers.provider.estimateGas(deployTx);
    const deploymentCost = gasEstimate * (gasPrice.gasPrice || 0n);
    
    console.log(`⛽ Estimated Gas: ${gasEstimate.toLocaleString()}`);
    console.log(`💸 Estimated Cost: ${ethers.formatEther(deploymentCost)} ETH`);
    
    if (initialBalance < deploymentCost * 110n / 100n) { // 10% buffer
      throw new Error('Insufficient balance for deployment');
    }
    
    const collateralLoopFarming = await CollateralLoopFarming.deploy({
      gasLimit: gasEstimate * 120n / 100n, // 20% buffer
      maxFeePerGas: gasPrice.maxFeePerGas,
      maxPriorityFeePerGas: ethers.parseUnits('1', 'gwei') // Low priority fee
    });
    
    console.log('⏳ Waiting for deployment...');
    await collateralLoopFarming.waitForDeployment();
    
    const contractAddress = await collateralLoopFarming.getAddress();
    const receipt = await collateralLoopFarming.deploymentTransaction()?.wait();
    
    console.log(`✅ CollateralLoopFarming deployed: ${contractAddress}`);
    console.log(`📊 Gas used: ${receipt?.gasUsed.toLocaleString()}`);
    console.log(`💰 Actual cost: ${ethers.formatEther((receipt?.gasUsed || 0n) * (receipt?.gasPrice || 0n))} ETH`);
    
    // Verify the contract has code
    const code = await ethers.provider.getCode(contractAddress);
    if (code === '0x') {
      throw new Error('Contract deployment failed - no code at address');
    }
    
    console.log('✅ Contract code verified');
    
    // Test basic functionality
    console.log('\n🔍 TESTING CONTRACT FUNCTIONALITY...');
    console.log('─'.repeat(40));
    
    try {
      const stats = await collateralLoopFarming.getStats();
      console.log('✅ getStats() function works');
      console.log(`📊 Total Profit: ${ethers.formatUnits(stats.totalProfit, 6)} USDC`);
      console.log(`📊 Total Executions: ${stats.totalExecutions}`);
      console.log(`📊 Emergency Stop: ${stats.isEmergencyStop}`);
    } catch (error) {
      console.log('⚠️  getStats() test failed:', error.message);
    }
    
    // Check final balance
    const finalBalance = await ethers.provider.getBalance(deployerAddress);
    const totalCost = initialBalance - finalBalance;
    
    console.log('\n💰 DEPLOYMENT SUMMARY');
    console.log('─'.repeat(30));
    console.log(`💸 Total Cost: ${ethers.formatEther(totalCost)} ETH`);
    console.log(`💰 Remaining Balance: ${ethers.formatEther(finalBalance)} ETH`);
    
    // Save deployment info
    const deploymentInfo = {
      contractAddress,
      deploymentBlock: receipt?.blockNumber,
      gasUsed: receipt?.gasUsed.toString(),
      cost: totalCost.toString(),
      timestamp: Date.now()
    };
    
    const fs = require('fs');
    const path = require('path');
    const deploymentFile = path.join(__dirname, '..', 'deployed-collateral-farming.json');
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log(`📄 Deployment info saved to: deployed-collateral-farming.json`);
    
    console.log('\n🎉 MINIMAL DEPLOYMENT COMPLETE!');
    console.log('═'.repeat(50));
    console.log('📋 Next Steps:');
    console.log('1. Test the deployed contract with small amounts');
    console.log('2. Execute profitable strategies to generate funds');
    console.log('3. Deploy remaining contracts with profits');
    console.log('4. Build complete system incrementally');
    
    return {
      success: true,
      contractAddress,
      cost: totalCost
    };
    
  } catch (error) {
    console.error('\n💥 DEPLOYMENT FAILED:', error.message);
    
    const finalBalance = await ethers.provider.getBalance(deployerAddress);
    const partialCost = initialBalance - finalBalance;
    
    console.log(`💸 Partial Cost: ${ethers.formatEther(partialCost)} ETH`);
    
    return {
      success: false,
      error: error.message,
      cost: partialCost
    };
  }
}

// Execute deployment
if (require.main === module) {
  deployMinimal()
    .then((result) => {
      if (result.success) {
        console.log('\n✅ Deployment successful!');
        process.exit(0);
      } else {
        console.log('\n❌ Deployment failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { deployMinimal };
