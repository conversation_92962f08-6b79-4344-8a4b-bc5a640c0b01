const { ethers } = require('ethers');
require('dotenv').config();

async function checkBalance() {
  try {
    console.log('🔍 CHECKING WALLET STATUS FOR DEPLOYMENT');
    console.log('═'.repeat(60));

    // Debug environment variables
    console.log('🔧 Environment Check:');
    console.log('- MAINNET_RPC_URL:', process.env.MAINNET_RPC_URL ? 'Set' : 'Missing');
    console.log('- PRIVATE_KEY:', process.env.PRIVATE_KEY ? 'Set' : 'Missing');
    console.log('- PROFIT_WALLET_ADDRESS:', process.env.PROFIT_WALLET_ADDRESS ? 'Set' : 'Missing');
    console.log('');
    
    const provider = new ethers.JsonRpcProvider(process.env.MAINNET_RPC_URL);
    const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
    
    console.log('📋 Wallet Address:', wallet.address);
    console.log('🎯 Profit Wallet:', process.env.PROFIT_WALLET_ADDRESS);
    
    const balance = await provider.getBalance(wallet.address);
    console.log('💰 ETH Balance:', ethers.formatEther(balance), 'ETH');
    
    const gasPrice = await provider.getFeeData();
    console.log('⛽ Current Gas Price:', ethers.formatUnits(gasPrice.gasPrice || 0n, 'gwei'), 'gwei');
    console.log('⛽ Max Fee Per Gas:', ethers.formatUnits(gasPrice.maxFeePerGas || 0n, 'gwei'), 'gwei');
    
    const blockNumber = await provider.getBlockNumber();
    console.log('🔗 Current Block:', blockNumber);
    
    // Check if we have enough for deployment (estimate ~0.5 ETH needed)
    const minRequired = ethers.parseEther('0.5');
    const deploymentCost = ethers.parseEther('0.3'); // Estimated deployment cost
    
    console.log('\n📊 DEPLOYMENT READINESS CHECK');
    console.log('─'.repeat(40));
    console.log('💸 Estimated Deployment Cost: 0.3 ETH');
    console.log('🔒 Minimum Required Balance: 0.5 ETH');
    
    if (balance >= minRequired) {
      console.log('✅ SUFFICIENT BALANCE FOR DEPLOYMENT');
      console.log('🚀 Ready to deploy advanced strategies!');
    } else {
      console.log('❌ INSUFFICIENT BALANCE');
      console.log('💡 Need at least 0.5 ETH for safe deployment');
      console.log('📈 Current shortfall:', ethers.formatEther(minRequired - balance), 'ETH');
    }
    
    // Check API connectivity
    console.log('\n🔗 API CONNECTIVITY CHECK');
    console.log('─'.repeat(40));
    
    try {
      const network = await provider.getNetwork();
      console.log('✅ Alchemy RPC:', network.name, 'Chain ID:', network.chainId);
    } catch (error) {
      console.log('❌ Alchemy RPC connection failed');
    }
    
    // Check Balancer Vault
    const BALANCER_VAULT = '******************************************';
    try {
      const vaultCode = await provider.getCode(BALANCER_VAULT);
      if (vaultCode !== '0x') {
        console.log('✅ Balancer V2 Vault accessible');
      } else {
        console.log('❌ Balancer V2 Vault not found');
      }
    } catch (error) {
      console.log('❌ Error checking Balancer Vault');
    }
    
    console.log('\n🎯 DEPLOYMENT TARGETS');
    console.log('─'.repeat(40));
    console.log('📦 CollateralLoopFarming.sol');
    console.log('📦 LPRewardHarvesting.sol');
    console.log('📦 MEVSandwichBot.sol');
    console.log('📦 AdvancedFlashLoanManager.sol');
    
  } catch (error) {
    console.error('💥 Error checking wallet status:', error.message);
  }
}

checkBalance();
