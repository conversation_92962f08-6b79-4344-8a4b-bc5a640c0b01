import { ethers } from 'hardhat';
import { config } from '../src/config';
import fs from 'fs';
import path from 'path';

/**
 * Deploy Advanced Flash Loan Strategies
 * Deploys all three strategy contracts and the unified manager
 */

interface DeploymentResult {
  collateralLoopFarming: string;
  lpRewardHarvesting: string;
  mevSandwichBot: string;
  advancedFlashLoanManager: string;
  deploymentBlock: number;
  gasUsed: bigint;
  totalCost: bigint;
}

async function main() {
  console.log('\n🚀 DEPLOYING ADVANCED FLASH LOAN STRATEGIES');
  console.log('═'.repeat(60));

  const [deployer] = await ethers.getSigners();
  const deployerAddress = await deployer.getAddress();
  const initialBalance = await ethers.provider.getBalance(deployerAddress);

  console.log(`📋 Deployer: ${deployerAddress}`);
  console.log(`💰 Initial Balance: ${ethers.formatEther(initialBalance)} ETH`);
  console.log(`🌐 Network: ${(await ethers.provider.getNetwork()).name}`);
  console.log(`⛽ Gas Price: ${ethers.formatUnits(await ethers.provider.getFeeData().then(f => f.gasPrice || 0n), 'gwei')} gwei`);

  // Validate minimum balance
  const minBalance = ethers.parseEther('0.5'); // 0.5 ETH minimum
  if (initialBalance < minBalance) {
    throw new Error(`Insufficient balance. Need at least ${ethers.formatEther(minBalance)} ETH`);
  }

  let totalGasUsed = 0n;
  const deploymentResults: Partial<DeploymentResult> = {};

  try {
    // Step 1: Deploy Collateral Loop Farming Contract
    console.log('\n📦 STEP 1: Deploying Collateral Loop Farming Contract...');
    console.log('─'.repeat(50));
    
    const CollateralLoopFarming = await ethers.getContractFactory('CollateralLoopFarming');
    const collateralLoopFarming = await CollateralLoopFarming.deploy({
      gasLimit: 3000000, // 3M gas limit
      maxFeePerGas: ethers.parseUnits('100', 'gwei'),
      maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei')
    });
    
    await collateralLoopFarming.waitForDeployment();
    const collateralAddress = await collateralLoopFarming.getAddress();
    const collateralReceipt = await collateralLoopFarming.deploymentTransaction()?.wait();
    
    deploymentResults.collateralLoopFarming = collateralAddress;
    totalGasUsed += collateralReceipt?.gasUsed || 0n;
    
    console.log(`✅ Collateral Loop Farming deployed: ${collateralAddress}`);
    console.log(`⛽ Gas used: ${collateralReceipt?.gasUsed.toLocaleString()}`);

    // Step 2: Deploy LP Reward Harvesting Contract
    console.log('\n📦 STEP 2: Deploying LP Reward Harvesting Contract...');
    console.log('─'.repeat(50));
    
    const LPRewardHarvesting = await ethers.getContractFactory('LPRewardHarvesting');
    const lpRewardHarvesting = await LPRewardHarvesting.deploy({
      gasLimit: 3000000,
      maxFeePerGas: ethers.parseUnits('100', 'gwei'),
      maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei')
    });
    
    await lpRewardHarvesting.waitForDeployment();
    const lpAddress = await lpRewardHarvesting.getAddress();
    const lpReceipt = await lpRewardHarvesting.deploymentTransaction()?.wait();
    
    deploymentResults.lpRewardHarvesting = lpAddress;
    totalGasUsed += lpReceipt?.gasUsed || 0n;
    
    console.log(`✅ LP Reward Harvesting deployed: ${lpAddress}`);
    console.log(`⛽ Gas used: ${lpReceipt?.gasUsed.toLocaleString()}`);

    // Step 3: Deploy MEV Sandwich Bot Contract
    console.log('\n📦 STEP 3: Deploying MEV Sandwich Bot Contract...');
    console.log('─'.repeat(50));
    
    const MEVSandwichBot = await ethers.getContractFactory('MEVSandwichBot');
    const mevSandwichBot = await MEVSandwichBot.deploy({
      gasLimit: 3000000,
      maxFeePerGas: ethers.parseUnits('100', 'gwei'),
      maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei')
    });
    
    await mevSandwichBot.waitForDeployment();
    const mevAddress = await mevSandwichBot.getAddress();
    const mevReceipt = await mevSandwichBot.deploymentTransaction()?.wait();
    
    deploymentResults.mevSandwichBot = mevAddress;
    totalGasUsed += mevReceipt?.gasUsed || 0n;
    
    console.log(`✅ MEV Sandwich Bot deployed: ${mevAddress}`);
    console.log(`⛽ Gas used: ${mevReceipt?.gasUsed.toLocaleString()}`);

    // Step 4: Deploy Advanced Flash Loan Manager
    console.log('\n📦 STEP 4: Deploying Advanced Flash Loan Manager...');
    console.log('─'.repeat(50));
    
    const AdvancedFlashLoanManager = await ethers.getContractFactory('AdvancedFlashLoanManager');
    const advancedFlashLoanManager = await AdvancedFlashLoanManager.deploy(
      collateralAddress,
      lpAddress,
      mevAddress,
      {
        gasLimit: 3000000,
        maxFeePerGas: ethers.parseUnits('100', 'gwei'),
        maxPriorityFeePerGas: ethers.parseUnits('2', 'gwei')
      }
    );
    
    await advancedFlashLoanManager.waitForDeployment();
    const managerAddress = await advancedFlashLoanManager.getAddress();
    const managerReceipt = await advancedFlashLoanManager.deploymentTransaction()?.wait();
    
    deploymentResults.advancedFlashLoanManager = managerAddress;
    totalGasUsed += managerReceipt?.gasUsed || 0n;
    
    console.log(`✅ Advanced Flash Loan Manager deployed: ${managerAddress}`);
    console.log(`⛽ Gas used: ${managerReceipt?.gasUsed.toLocaleString()}`);

    // Step 5: Configure Contracts
    console.log('\n⚙️  STEP 5: Configuring Contracts...');
    console.log('─'.repeat(50));

    // Whitelist some initial LP pools for harvesting
    const initialPools = [
      '******************************************', // Curve 3Pool
      '******************************************', // Curve SETH/ETH
      '******************************************'  // Curve ETH/stETH
    ];

    console.log('🔧 Whitelisting LP pools...');
    for (const pool of initialPools) {
      try {
        const tx = await lpRewardHarvesting.setPoolWhitelist(pool, true, {
          gasLimit: 100000,
          maxFeePerGas: ethers.parseUnits('100', 'gwei')
        });
        await tx.wait();
        console.log(`   ✅ Whitelisted pool: ${pool}`);
      } catch (error) {
        console.log(`   ❌ Failed to whitelist pool: ${pool}`);
      }
    }

    // Add initial MEV opportunities
    console.log('🔧 Adding MEV opportunities...');
    const mevPools = [
      {
        pool: '******************************************', // USDC/ETH 0.05%
        token0: '******************************************', // USDC
        token1: '******************************************', // WETH
        minAmount: ethers.parseEther('100'),
        expectedProfit: ethers.parseUnits('1000', 6)
      }
    ];

    for (const opportunity of mevPools) {
      try {
        const tx = await mevSandwichBot.addOpportunity(
          opportunity.pool,
          opportunity.token0,
          opportunity.token1,
          opportunity.minAmount,
          opportunity.expectedProfit,
          {
            gasLimit: 150000,
            maxFeePerGas: ethers.parseUnits('100', 'gwei')
          }
        );
        await tx.wait();
        console.log(`   ✅ Added MEV opportunity: ${opportunity.pool}`);
      } catch (error) {
        console.log(`   ❌ Failed to add MEV opportunity: ${opportunity.pool}`);
      }
    }

    // Step 6: Verify Deployments
    console.log('\n🔍 STEP 6: Verifying Deployments...');
    console.log('─'.repeat(50));

    // Verify each contract has code
    const contracts = [
      { name: 'Collateral Loop Farming', address: collateralAddress },
      { name: 'LP Reward Harvesting', address: lpAddress },
      { name: 'MEV Sandwich Bot', address: mevAddress },
      { name: 'Advanced Flash Loan Manager', address: managerAddress }
    ];

    for (const contract of contracts) {
      const code = await ethers.provider.getCode(contract.address);
      if (code === '0x') {
        throw new Error(`${contract.name} deployment failed - no code at address`);
      }
      console.log(`✅ ${contract.name}: Code verified`);
    }

    // Calculate final costs
    const finalBalance = await ethers.provider.getBalance(deployerAddress);
    const totalCost = initialBalance - finalBalance;
    const currentBlock = await ethers.provider.getBlockNumber();

    const finalResults: DeploymentResult = {
      collateralLoopFarming: collateralAddress,
      lpRewardHarvesting: lpAddress,
      mevSandwichBot: mevAddress,
      advancedFlashLoanManager: managerAddress,
      deploymentBlock: currentBlock,
      gasUsed: totalGasUsed,
      totalCost
    };

    // Step 7: Save Deployment Results
    console.log('\n💾 STEP 7: Saving Deployment Results...');
    console.log('─'.repeat(50));

    const deploymentFile = path.join(__dirname, '..', 'deployed-advanced-strategies.json');
    fs.writeFileSync(deploymentFile, JSON.stringify(finalResults, null, 2));
    console.log(`✅ Deployment results saved to: ${deploymentFile}`);

    // Step 8: Display Summary
    console.log('\n🎉 DEPLOYMENT COMPLETE!');
    console.log('═'.repeat(60));
    console.log(`📋 Collateral Loop Farming: ${collateralAddress}`);
    console.log(`📋 LP Reward Harvesting:    ${lpAddress}`);
    console.log(`📋 MEV Sandwich Bot:        ${mevAddress}`);
    console.log(`📋 Manager Contract:        ${managerAddress}`);
    console.log(`📊 Total Gas Used:          ${totalGasUsed.toLocaleString()}`);
    console.log(`💰 Total Cost:              ${ethers.formatEther(totalCost)} ETH`);
    console.log(`🏦 Profit Wallet:           ******************************************`);
    console.log(`🔗 Deployment Block:        ${currentBlock}`);

    console.log('\n📝 NEXT STEPS:');
    console.log('1. Update contract addresses in TypeScript files');
    console.log('2. Start the strategy monitor');
    console.log('3. Begin scanning for opportunities');
    console.log('4. Execute first strategy when profitable');

    return finalResults;

  } catch (error) {
    console.error('\n💥 DEPLOYMENT FAILED:', error);
    
    // Calculate partial costs
    const finalBalance = await ethers.provider.getBalance(deployerAddress);
    const partialCost = initialBalance - finalBalance;
    
    console.log(`💸 Partial Cost: ${ethers.formatEther(partialCost)} ETH`);
    
    throw error;
  }
}

// Execute deployment
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { main as deployAdvancedStrategies };
