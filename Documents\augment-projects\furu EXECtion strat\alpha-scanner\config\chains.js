const dotenv = require('dotenv');
dotenv.config();

const CHAINS = {
  ethereum: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    rpcUrl: process.env.MAINNET_RPC_URL,
    blockTime: 12,
    gasPrice: {
      fast: 30,
      standard: 20,
      safe: 15
    },
    contracts: {
      // Flash Loan Providers
      aavePool: '******************************************',
      balancerVault: '******************************************',
      
      // DEX Routers
      uniswapV3Router: '******************************************',
      uniswapV2Router: '******************************************',
      sushiswapRouter: '******************************************',
      curveRegistry: '******************************************',
      
      // Yield Protocols
      convexBooster: '******************************************',
      aaveIncentives: '******************************************',
      
      // Tokens
      weth: '******************************************',
      usdc: '******************************************',
      dai: '******************************************',
      usdt: '******************************************',
      wsteth: '******************************************',
      crv: '******************************************',
      cvx: '******************************************'
    },
    multicall: '******************************************'
  },
  
  optimism: {
    chainId: 10,
    name: 'Optimism',
    rpcUrl: process.env.OPTIMISM_RPC_URL,
    blockTime: 2,
    gasPrice: {
      fast: 0.002,
      standard: 0.001,
      safe: 0.0005
    },
    contracts: {
      // Flash Loan Providers
      aavePool: '******************************************',
      balancerVault: '******************************************',
      
      // DEX Routers
      uniswapV3Router: '******************************************',
      velodromeRouter: '******************************************',
      
      // Tokens
      weth: '******************************************',
      usdc: '******************************************',
      dai: '******************************************',
      usdt: '******************************************',
      op: '******************************************'
    },
    multicall: '******************************************'
  },
  
  arbitrum: {
    chainId: 42161,
    name: 'Arbitrum One',
    rpcUrl: process.env.ARBITRUM_RPC_URL,
    blockTime: 0.25,
    gasPrice: {
      fast: 0.1,
      standard: 0.05,
      safe: 0.02
    },
    contracts: {
      // Flash Loan Providers
      aavePool: '******************************************',
      balancerVault: '******************************************',
      
      // DEX Routers
      uniswapV3Router: '******************************************',
      sushiswapRouter: '******************************************',
      
      // Tokens
      weth: '******************************************',
      usdc: '******************************************',
      dai: '******************************************',
      usdt: '******************************************',
      arb: '******************************************'
    },
    multicall: '******************************************'
  }
};

const FLASH_LOAN_PROVIDERS = {
  balancer: {
    name: 'Balancer V2',
    fee: 0, // No fee for flash loans
    maxAmount: '1000000', // 1M tokens typical max
    interface: 'IFlashLoanRecipient'
  },
  aave: {
    name: 'Aave V3',
    fee: 0.0009, // 0.09%
    maxAmount: '500000', // 500K tokens typical max
    interface: 'IFlashLoanReceiver'
  },
  dydx: {
    name: 'dYdX',
    fee: 0.0002, // 0.02%
    maxAmount: '100000', // 100K tokens typical max
    interface: 'ICallee'
  }
};

const STRATEGY_CONFIG = {
  minProfitUSD: 100000, // $100K minimum
  maxGasLimit: 1000000, // 1M gas max
  maxSlippage: 0.005, // 0.5% max slippage
  simulationTimeout: 30000, // 30 seconds
  retryAttempts: 3,
  profitMargin: 0.2 // 20% safety margin
};

module.exports = {
  CHAINS,
  FLASH_LOAN_PROVIDERS,
  STRATEGY_CONFIG
};
