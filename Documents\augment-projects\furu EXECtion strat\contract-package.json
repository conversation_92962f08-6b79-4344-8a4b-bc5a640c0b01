{"name": "ultimate-arbitrage-bot", "version": "2.0.0", "description": "Final production arbitrage contract with 4 proven strategies", "main": "index.js", "scripts": {"compile": "hardhat compile", "deploy": "hardhat run scripts/deploy-ultimate-arbitrage.js --network optimism", "deploy-local": "hardhat run scripts/deploy-ultimate-arbitrage.js --network hardhat", "execute": "hardhat run scripts/execute-strategies.js --network optimism", "test": "hardhat test", "verify": "hardhat verify --network optimism", "clean": "hardhat clean", "size": "hardhat size-contracts", "install-deps": "npm install @aave/core-v3 @openzeppelin/contracts @uniswap/v2-periphery @uniswap/v3-periphery ethers hardhat @nomicfoundation/hardhat-toolbox @nomicfoundation/hardhat-verify hardhat-gas-reporter hardhat-contract-sizer dotenv"}, "keywords": ["defi", "arbitrage", "flash-loans", "mev", "optimism", "aave", "uniswap"], "author": "Ultimate Arbitrage Team", "license": "MIT", "dependencies": {"@aave/core-v3": "^1.19.3", "@openzeppelin/contracts": "^4.9.3", "@uniswap/v2-periphery": "^1.1.0-beta.0", "@uniswap/v3-periphery": "^1.4.4", "ethers": "^6.7.1", "hardhat": "^2.17.1", "dotenv": "^16.3.1"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^3.0.0", "@nomicfoundation/hardhat-verify": "^1.1.1", "hardhat-gas-reporter": "^1.0.9", "hardhat-contract-sizer": "^2.10.0", "solidity-coverage": "^0.8.4"}, "engines": {"node": ">=16.0.0"}}