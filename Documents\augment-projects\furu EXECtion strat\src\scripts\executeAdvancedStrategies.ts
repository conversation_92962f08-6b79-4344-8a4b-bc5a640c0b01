import { ethers } from 'ethers';
import { AdvancedStrategyExecutor, StrategyParams } from '../core/advancedStrategyExecutor';
import { StrategyMonitor, OpportunityAlert } from '../monitoring/strategyMonitor';
import { config } from '../config';
import { logger } from '../utils/logger';
import fs from 'fs';
import path from 'path';

/**
 * Advanced Strategy Execution Engine
 * Coordinates all three flash loan strategies with real-time monitoring
 */

interface ExecutionConfig {
  enableCollateralLoopFarming: boolean;
  enableLPRewardHarvesting: boolean;
  enableMEVSandwichBot: boolean;
  maxDailyExecutions: number;
  minProfitThreshold: bigint;
  maxGasPrice: bigint;
  emergencyStopOnFailures: number;
}

class AdvancedStrategyEngine {
  private executor: AdvancedStrategyExecutor;
  private monitor: StrategyMonitor;
  private isRunning: boolean = false;
  private executionCount: number = 0;
  private totalProfit: bigint = 0n;
  private failureCount: number = 0;

  private readonly config: ExecutionConfig = {
    enableCollateralLoopFarming: true,
    enableLPRewardHarvesting: true,
    enableMEVSandwichBot: true,
    maxDailyExecutions: 50,
    minProfitThreshold: ethers.parseUnits('500', 6), // $500 USDC
    maxGasPrice: ethers.parseUnits('200', 'gwei'), // 200 gwei
    emergencyStopOnFailures: 3
  };

  constructor() {
    this.executor = new AdvancedStrategyExecutor();
    this.monitor = new StrategyMonitor();
    
    this.setupEventListeners();
  }

  /**
   * Start the advanced strategy execution engine
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Strategy engine already running');
      return;
    }

    logger.info('🚀 STARTING ADVANCED STRATEGY EXECUTION ENGINE');
    logger.info('═'.repeat(60));

    try {
      // Validate environment
      await this.validateEnvironment();

      // Start monitoring
      await this.monitor.startMonitoring();

      // Set running flag
      this.isRunning = true;

      logger.info('✅ Advanced Strategy Engine Started Successfully');
      logger.info(`📊 Monitoring ${this.getEnabledStrategiesCount()} strategies`);
      logger.info(`💰 Min Profit Threshold: $${ethers.formatUnits(this.config.minProfitThreshold, 6)}`);
      logger.info(`⛽ Max Gas Price: ${ethers.formatUnits(this.config.maxGasPrice, 'gwei')} gwei`);

      // Keep the process running
      await this.runMainLoop();

    } catch (error) {
      logger.error('💥 Failed to start strategy engine', error);
      await this.stop();
      throw error;
    }
  }

  /**
   * Stop the strategy execution engine
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logger.info('🛑 STOPPING ADVANCED STRATEGY ENGINE');
    
    this.isRunning = false;
    this.monitor.stopMonitoring();
    
    // Save final statistics
    await this.saveFinalStats();
    
    logger.info('✅ Strategy Engine Stopped');
  }

  /**
   * Setup event listeners for monitoring and execution
   */
  private setupEventListeners(): void {
    // Listen for opportunity alerts
    this.monitor.on('opportunityDetected', async (opportunity: OpportunityAlert) => {
      await this.handleOpportunity(opportunity);
    });

    // Listen for risk alerts
    this.monitor.on('alert', (alert) => {
      logger.warn('🚨 RISK ALERT', alert);
      
      if (alert.severity === 'critical') {
        this.handleCriticalAlert(alert);
      }
    });

    // Listen for strategy execution results
    this.monitor.on('strategyExecuted', (result) => {
      logger.info('📊 STRATEGY EXECUTED', result);
      this.updateExecutionStats(result);
    });

    // Listen for performance updates
    this.monitor.on('performanceUpdated', (metrics) => {
      logger.debug('📈 PERFORMANCE UPDATE', {
        strategyType: metrics.strategyType,
        totalProfit: ethers.formatUnits(metrics.totalProfit, 6),
        successRate: metrics.successRate.toFixed(2)
      });
    });
  }

  /**
   * Main execution loop
   */
  private async runMainLoop(): Promise<void> {
    while (this.isRunning) {
      try {
        // Check if we've hit daily execution limit
        if (this.executionCount >= this.config.maxDailyExecutions) {
          logger.info('📊 Daily execution limit reached, waiting for next day');
          await this.sleep(3600000); // Wait 1 hour
          continue;
        }

        // Check for emergency stop conditions
        if (this.failureCount >= this.config.emergencyStopOnFailures) {
          logger.error('🚨 EMERGENCY STOP: Too many failures');
          await this.executor.emergencyStop();
          break;
        }

        // Force a monitoring cycle
        await this.monitor.forceMonitoringCycle();

        // Wait before next cycle
        await this.sleep(10000); // 10 seconds

      } catch (error) {
        logger.error('Error in main loop', error);
        await this.sleep(30000); // Wait 30 seconds on error
      }
    }
  }

  /**
   * Handle detected opportunities
   */
  private async handleOpportunity(opportunity: OpportunityAlert): Promise<void> {
    try {
      logger.info('🎯 OPPORTUNITY DETECTED', {
        strategyType: opportunity.strategyType,
        expectedProfit: ethers.formatUnits(opportunity.expectedProfit, 6),
        confidence: opportunity.confidence,
        urgency: opportunity.urgency
      });

      // Check if strategy is enabled
      if (!this.isStrategyEnabled(opportunity.strategyType)) {
        logger.warn(`Strategy ${opportunity.strategyType} is disabled, skipping`);
        return;
      }

      // Check profit threshold
      if (opportunity.expectedProfit < this.config.minProfitThreshold) {
        logger.warn('Opportunity below profit threshold, skipping');
        return;
      }

      // Check urgency and confidence
      if (opportunity.confidence < 70 && opportunity.urgency !== 'critical') {
        logger.warn('Opportunity confidence too low, skipping');
        return;
      }

      // Execute the strategy
      await this.executeOpportunity(opportunity);

    } catch (error) {
      logger.error('Error handling opportunity', error);
      this.failureCount++;
    }
  }

  /**
   * Execute a specific opportunity
   */
  private async executeOpportunity(opportunity: OpportunityAlert): Promise<void> {
    try {
      logger.info(`⚡ EXECUTING STRATEGY ${opportunity.strategyType}`);

      const params: StrategyParams = {
        strategyType: opportunity.strategyType,
        flashLoanAmount: this.calculateOptimalFlashLoanAmount(opportunity),
        targetAddress: opportunity.pool,
        additionalParams: opportunity.parameters
      };

      const result = await this.executor.executeStrategy(params);

      if (result.success) {
        logger.info('✅ STRATEGY EXECUTION SUCCESSFUL', {
          strategyType: result.strategyType,
          profit: result.profit ? ethers.formatUnits(result.profit, 6) : '0',
          txHash: result.txHash
        });

        this.executionCount++;
        this.totalProfit += result.profit || 0n;
        this.failureCount = Math.max(0, this.failureCount - 1); // Reduce failure count on success

      } else {
        logger.error('❌ STRATEGY EXECUTION FAILED', {
          strategyType: result.strategyType,
          error: result.error
        });

        this.failureCount++;
      }

    } catch (error) {
      logger.error('Error executing opportunity', error);
      this.failureCount++;
    }
  }

  /**
   * Calculate optimal flash loan amount based on opportunity
   */
  private calculateOptimalFlashLoanAmount(opportunity: OpportunityAlert): bigint {
    switch (opportunity.strategyType) {
      case 1: // Collateral Loop Farming
        return ethers.parseEther('750'); // 750 ETH
      case 2: // LP Reward Harvesting
        return ethers.parseEther('1500000'); // 1.5M tokens
      case 3: // MEV Sandwich
        return ethers.parseEther('500'); // 500 ETH
      default:
        return ethers.parseEther('500');
    }
  }

  /**
   * Handle critical alerts
   */
  private handleCriticalAlert(alert: any): void {
    logger.error('🚨 CRITICAL ALERT RECEIVED', alert);

    if (alert.type === 'emergency_stop' || alert.type === 'emergency_stop_activated') {
      this.stop();
    }
  }

  /**
   * Update execution statistics
   */
  private updateExecutionStats(result: any): void {
    // Update internal tracking
    logger.debug('Updating execution stats', result);
  }

  /**
   * Validate environment before starting
   */
  private async validateEnvironment(): Promise<void> {
    // Check if deployment file exists
    const deploymentFile = path.join(__dirname, '..', '..', 'deployed-advanced-strategies.json');
    if (!fs.existsSync(deploymentFile)) {
      throw new Error('Deployment file not found. Please deploy contracts first.');
    }

    // Check wallet balance
    const provider = new ethers.JsonRpcProvider(config.rpc.mainnet);
    const wallet = new ethers.Wallet(config.wallet.privateKey, provider);
    const balance = await provider.getBalance(wallet.address);
    
    if (balance < ethers.parseEther('0.1')) {
      throw new Error('Insufficient ETH balance for gas fees');
    }

    logger.info('✅ Environment validation passed');
  }

  /**
   * Check if strategy is enabled
   */
  private isStrategyEnabled(strategyType: 1 | 2 | 3): boolean {
    switch (strategyType) {
      case 1: return this.config.enableCollateralLoopFarming;
      case 2: return this.config.enableLPRewardHarvesting;
      case 3: return this.config.enableMEVSandwichBot;
      default: return false;
    }
  }

  /**
   * Get count of enabled strategies
   */
  private getEnabledStrategiesCount(): number {
    let count = 0;
    if (this.config.enableCollateralLoopFarming) count++;
    if (this.config.enableLPRewardHarvesting) count++;
    if (this.config.enableMEVSandwichBot) count++;
    return count;
  }

  /**
   * Save final statistics
   */
  private async saveFinalStats(): Promise<void> {
    const stats = {
      totalExecutions: this.executionCount,
      totalProfit: this.totalProfit.toString(),
      totalProfitUSD: ethers.formatUnits(this.totalProfit, 6),
      failureCount: this.failureCount,
      timestamp: Date.now()
    };

    const statsFile = path.join(__dirname, '..', '..', 'execution-stats.json');
    fs.writeFileSync(statsFile, JSON.stringify(stats, null, 2));
    
    logger.info('📊 FINAL STATISTICS', stats);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution function
async function main() {
  const engine = new AdvancedStrategyEngine();

  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    await engine.stop();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    await engine.stop();
    process.exit(0);
  });

  // Start the engine
  await engine.start();
}

// Execute if run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Engine failed to start:', error);
    process.exit(1);
  });
}

export { AdvancedStrategyEngine };
